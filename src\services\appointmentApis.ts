import axios from "axios";
import type {
  Appointment,
  CreateAppointmentPayload,
  UpdateAppointmentPayload,
  AppointmentFilters,
  AppointmentStats,
  ProviderSchedule,
  TimeSlot
} from '../types/appointment';


// Backend API Configuration
const BASE_URL = "https://megha-dev.sirobilt.com";

// Configure axios defaults
axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.headers.common['Accept'] = 'application/json';

// Add request interceptor for authentication
axios.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken') || localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add facility context if available
    const facilityId = localStorage.getItem('selectedFacilityId');
    if (facilityId) {
      config.headers['X-Facility-Id'] = facilityId;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', {
      status: error.response?.status,
      message: error.response?.data?.message || error.message,
      data: error.response?.data,
      url: error.config?.url
    });

    // Handle authentication errors
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
      localStorage.removeItem('accessToken');
      // Redirect to login if needed
    }

    return Promise.reject(error);
  }
);

// Backend DTO interfaces based on OpenAPI spec
interface AppointmentDTO {
  id?: string; // UUID from backend
  appointmentId?: string; // Legacy field for backward compatibility
  patientId: string;
  providerId: string;
  facilityId: string;
  appointmentDate: string;
  startTime: string;
  endTime: string;
  duration?: number;
  slotNumber?: number; // From /api/consultants/{consultantId}/slots
  type: string;
  status: string;
  priority: string;
  title?: string;
  description?: string;
  notes?: string;
  reason?: string;
  isRecurring?: boolean;
  recurringPattern?: string;
  recurringEndDate?: string;
  parentAppointmentId?: string;
  externalSystemId?: string;
  externalSystemName?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
  // Nested objects from API response
  patient?: {
    firstName?: string;
    lastName?: string;
    fullName?: string;
  };
  provider?: {
    firstName?: string;
    lastName?: string;
    fullName?: string;
  };
  facility?: {
    facilityName?: string;
    address?: string;
  };
}

interface ApiResponseAppointmentResponseDTO {
  success: boolean;
  data: AppointmentDTO;
  message?: string;
  error?: string;
}

interface ApiResponseAppointmentDTO {
  success: boolean;
  data: AppointmentDTO;
  message?: string;
  error?: string;
}



interface CancelAppointmentDto {
  reason: string;
  notes?: string;
  notifyPatient: boolean;
}

interface ConfirmAppointmentDto {
  notes?: string;
  notifyPatient: boolean;
}

// Helper function to transform frontend payload to backend DTO
const transformToAppointmentDTO = (payload: CreateAppointmentPayload | UpdateAppointmentPayload): AppointmentDTO => {
  // Helper function to ensure datetime format
  const ensureDateTimeFormat = (dateTime: string, fallbackDate?: string, fallbackTime?: string): string => {
    if (!dateTime) return "";

    // If already in datetime format, return as is
    if (dateTime.includes('T')) return dateTime;

    // If it's a date-only format and we have fallback time, combine them
    if (dateTime.match(/^\d{4}-\d{2}-\d{2}$/) && fallbackTime) {
      return `${dateTime}T${fallbackTime}:00`;
    }

    // If it's a time-only format and we have fallback date, combine them
    if (dateTime.match(/^\d{2}:\d{2}$/) && fallbackDate) {
      return `${fallbackDate}T${dateTime}:00`;
    }

    return dateTime;
  };

  // Transform all datetime fields
  const appointmentDate = ensureDateTimeFormat(
    payload.appointmentDate!,
    payload.appointmentDate?.split('T')[0],
    payload.startTime
  );

  const startTime = ensureDateTimeFormat(
    payload.startTime!,
    payload.appointmentDate?.split('T')[0],
    payload.startTime
  );

  const endTime = ensureDateTimeFormat(
    payload.endTime!,
    payload.appointmentDate?.split('T')[0],
    payload.endTime
  );

  const recurringEndDate = payload.recurringEndDate ?
    ensureDateTimeFormat(payload.recurringEndDate) :
    payload.recurringEndDate;

  const dto: AppointmentDTO = {
    patientId: payload.patientId!,
    providerId: payload.providerId!,
    facilityId: payload.facilityId!,
    appointmentDate: appointmentDate,
    startTime: startTime,
    endTime: endTime,
    duration: typeof payload.duration === 'string' ? parseInt(payload.duration) : payload.duration,
    slotNumber: payload.slotNumber,
    type: payload.type!,
    status: (payload as any).status || "Scheduled",
    priority: payload.priority!,
    title: payload.title,
    description: payload.description,
    notes: payload.notes,
    reason: payload.reason,
    isRecurring: payload.isRecurring,
    recurringPattern: payload.recurringPattern,
    recurringEndDate: recurringEndDate,
    createdBy: localStorage.getItem('userId') || 'system',
    updatedBy: localStorage.getItem('userId') || 'system'
  };

  // For update operations, include the id field if available
  if ((payload as any).appointmentId) {
    dto.id = (payload as any).appointmentId;
  }

  return dto;
};

// Helper function to transform backend DTO to frontend appointment
const transformToAppointment = (dto: AppointmentDTO): Appointment => {
  // Use the UUID 'id' field from backend as appointmentId
  // Priority: id > appointmentId > generated fallback
  const appointmentId = dto.id ||
    dto.appointmentId ||
    `${dto.patientId}-${dto.providerId}-${dto.appointmentDate.replace(/:/g, '-')}-${dto.startTime.replace(/:/g, '-')}`;



  return {
    appointmentId,
    patientId: dto.patientId,
    providerId: dto.providerId,
    facilityId: dto.facilityId,
    appointmentDate: dto.appointmentDate,
    startTime: dto.startTime,
    endTime: dto.endTime,
    duration: dto.duration as any,
    slotNumber: dto.slotNumber,
    type: dto.type as any,
    status: dto.status as any,
    priority: dto.priority as any,
    title: dto.title,
    description: dto.description,
    notes: dto.notes,
    reason: dto.reason,
    isRecurring: dto.isRecurring,
    recurringPattern: dto.recurringPattern as any,
    recurringEndDate: dto.recurringEndDate,
    parentAppointmentId: dto.parentAppointmentId,
    externalSystemId: dto.externalSystemId,
    externalSystemName: dto.externalSystemName,
    createdAt: dto.createdAt,
    updatedAt: dto.updatedAt,
    createdBy: dto.createdBy,
    updatedBy: dto.updatedBy,
    // Include nested objects from API response
    patient: dto.patient,
    provider: dto.provider,
    facility: dto.facility
  };
};

// POST /api/appointments - Create new appointment
export const createAppointment = async (payload: CreateAppointmentPayload) => {
  try {
    console.log("Creating appointment with payload:", payload);

    const appointmentDTO = transformToAppointmentDTO(payload);
    const response = await axios.post<ApiResponseAppointmentResponseDTO>(`${BASE_URL}/api/appointments`, appointmentDTO);

    if (response.data.success && response.data.data) {
      const appointment = transformToAppointment(response.data.data);
      return {
        success: true,
        data: appointment,
        message: response.data.message || "Appointment created successfully"
      };
    } else {
      return {
        success: false,
        error: response.data.error || "Failed to create appointment"
      };
    }
  } catch (error: any) {
    console.error("Create Appointment Error:", error.response?.data || error.message);

    const errorMessage = error.response?.data?.message ||
                        error.response?.data?.error ||
                        error.message ||
                        "Failed to create appointment";

    return {
      success: false,
      error: errorMessage,
      details: error.response?.data
    };
  }
};

// GET /api/appointments - List all appointments
export const getAllAppointments = async (): Promise<Appointment[]> => {
  try {
    console.log("Fetching all appointments");

    const response = await axios.get<AppointmentDTO[]>(`${BASE_URL}/api/appointments`);

    return response.data.map(transformToAppointment);
  } catch (error: any) {
    console.error("Fetch All Appointments Error:", error.response?.data || error.message);
    throw new Error(error.response?.data?.message || "Failed to fetch appointments");
  }
};

// Consolidated GET /api/appointments/query - Single method for all appointment queries
export const getAppointments = async (filters: AppointmentFilters = {}) => {
  try {
    console.log("Fetching appointments with filters:", filters);

    const params = new URLSearchParams();

    // Add filter parameters according to OpenAPI spec
    if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters.dateTo) params.append('dateTo', filters.dateTo);
    if (filters.facilityId) params.append('facilityId', filters.facilityId);
    if (filters.patientId) params.append('patientId', filters.patientId);
    if (filters.providerId) params.append('providerId', filters.providerId);
    if (filters.searchTerm) params.append('searchTerm', filters.searchTerm);

    // Handle array parameters
    if (filters.status?.length) {
      filters.status.forEach(status => params.append('status', status));
    }
    if (filters.type?.length) {
      filters.type.forEach(type => params.append('type', type));
    }
    if (filters.priority?.length) {
      filters.priority.forEach(priority => params.append('priority', priority));
    }

    // Pagination parameters
    const page = filters.page || 0;
    const size = filters.size || 20;
    params.append('page', page.toString());
    params.append('size', size.toString());

    const response = await axios.get(`${BASE_URL}/api/appointments/query?${params}`);

    const responseData = response.data;

    // Handle the new API response structure
    let appointments = [];
    let pagination = {
      totalElements: 0,
      totalPages: 1,
      page: 0,
      size: 20,
      first: true,
      last: true,
      numberOfElements: 0
    };

    if (responseData.success && responseData.data && responseData.data.appointments) {
      // New API structure with success wrapper
      appointments = responseData.data.appointments.map(transformToAppointment);

      if (responseData.pagination) {
        pagination = {
          totalElements: responseData.pagination.totalElements || appointments.length,
          totalPages: responseData.pagination.totalPages || 1,
          page: responseData.pagination.page || 0,
          size: responseData.pagination.size || appointments.length,
          first: (responseData.pagination.page || 0) === 0,
          last: (responseData.pagination.page || 0) >= (responseData.pagination.totalPages || 1) - 1,
          numberOfElements: appointments.length
        };
      }
    } else if (responseData.content && Array.isArray(responseData.content)) {
      // Legacy structure
      appointments = responseData.content.map(transformToAppointment);
      pagination = {
        totalElements: responseData.totalElements || appointments.length,
        totalPages: responseData.totalPages || 1,
        page: responseData.number || 0,
        size: responseData.size || appointments.length,
        first: responseData.first !== undefined ? responseData.first : true,
        last: responseData.last !== undefined ? responseData.last : true,
        numberOfElements: responseData.numberOfElements || appointments.length
      };
    } else if (Array.isArray(responseData)) {
      // Direct array response
      appointments = responseData.map(transformToAppointment);
      pagination.numberOfElements = appointments.length;
      pagination.totalElements = appointments.length;
    } else {
      console.warn("Unexpected response structure:", responseData);
    }

    return {
      results: appointments,
      ...pagination
    };
  } catch (error: any) {
    console.error("Failed to fetch appointments:", error.response?.data || error.message);

    // Return empty result set on error
    return {
      results: [],
      totalElements: 0,
      totalPages: 0,
      page: 0,
      size: 20,
      first: true,
      last: true,
      numberOfElements: 0,
      error: error.response?.data?.message || error.message
    };
  }
};

// Convenience method for provider appointments
export const getAppointmentsByProvider = async (providerId: string, dateFrom?: string, dateTo?: string): Promise<Appointment[]> => {
  console.log('🔍 getAppointmentsByProvider called with:', {
    providerId,
    dateFrom,
    dateTo
  });

  const filters: AppointmentFilters = {
    providerId,
    page: 0,
    size: 100
  };

  if (dateFrom) filters.dateFrom = dateFrom;
  if (dateTo) filters.dateTo = dateTo;

  console.log('📋 Calling getAppointments with filters:', filters);

  const response = await getAppointments(filters);

  console.log('✅ getAppointmentsByProvider response:', {
    totalResults: response.results.length,
    results: response.results.map(apt => ({
      id: apt.appointmentId,
      providerId: apt.providerId,
      date: apt.appointmentDate,
      time: apt.startTime,
      patient: apt.patient?.firstName + ' ' + apt.patient?.lastName
    }))
  });

  return response.results;
};

// GET /api/appointments/{id} - Get appointment by ID
export const getAppointmentById = async (id: string): Promise<Appointment> => {
  try {
    console.log("Fetching appointment by ID:", id);

    const response = await axios.get<ApiResponseAppointmentDTO>(`${BASE_URL}/api/appointments/${id}`);

    if (response.data.success && response.data.data) {
      return transformToAppointment(response.data.data);
    } else {
      throw new Error(response.data.error || "Failed to fetch appointment");
    }
  } catch (error: any) {
    console.error("Fetch Appointment Error:", error.response?.data || error.message);

    const errorMessage = error.response?.data?.message ||
                        error.response?.data?.error ||
                        "Failed to fetch appointment data";

    throw new Error(errorMessage);
  }
};

// PUT /api/appointments/{appointmentId} - Update appointment
export const updateAppointment = async (appointmentId: string, payload: UpdateAppointmentPayload) => {
  try {
    console.log("Updating appointment:", appointmentId, payload);

    // Add the appointmentId to the payload for the DTO transformation
    const payloadWithId = { ...payload, appointmentId };
    const appointmentDTO = transformToAppointmentDTO(payloadWithId);

    console.log("Sending update request with DTO:", appointmentDTO);

    const response = await axios.put<AppointmentDTO>(`${BASE_URL}/api/appointments/${appointmentId}`, appointmentDTO);

    console.log("Update response:", response.data);

    const appointment = transformToAppointment(response.data);

    return {
      success: true,
      data: appointment,
      message: "Appointment updated successfully"
    };
  } catch (error: any) {
    console.error("Update Appointment Error:", error.response?.data || error.message);

    const errorMessage = error.response?.data?.message ||
                        error.response?.data?.error ||
                        error.message ||
                        "Failed to update appointment";

    return {
      success: false,
      error: errorMessage,
      details: error.response?.data
    };
  }
};





// DELETE /api/appointments/{id} - Delete appointment
export const deleteAppointment = async (id: string) => {
  try {
    console.log("Deleting appointment:", id);

    await axios.delete(`${BASE_URL}/api/appointments/${id}`);

    return {
      success: true,
      message: "Appointment deleted successfully"
    };
  } catch (error: any) {
    console.error("Delete Appointment Error:", error.response?.data || error.message);

    const errorMessage = error.response?.data?.message ||
                        error.response?.data?.error ||
                        error.message ||
                        "Failed to delete appointment";

    return {
      success: false,
      error: errorMessage,
      details: error.response?.data
    };
  }
};

// PATCH /api/appointments/{appointmentId}/cancel - Cancel appointment
export const cancelAppointment = async (
  appointmentId: string,
  reason: string,
  notes?: string,
  notifyPatient: boolean = true
) => {
  try {
    console.log("Cancelling appointment:", appointmentId, { reason, notes, notifyPatient });

    const cancelDto: CancelAppointmentDto = {
      reason,
      notes,
      notifyPatient
    };

    const response = await axios.patch<ApiResponseAppointmentResponseDTO>(
      `${BASE_URL}/api/appointments/${appointmentId}/cancel`,
      cancelDto
    );

    if (response.data.success && response.data.data) {
      const appointment = transformToAppointment(response.data.data);
      return {
        success: true,
        data: appointment,
        message: response.data.message || "Appointment cancelled successfully"
      };
    } else {
      return {
        success: false,
        error: response.data.error || "Failed to cancel appointment"
      };
    }
  } catch (error: any) {
    console.error("Cancel Appointment Error:", error.response?.data || error.message);

    const errorMessage = error.response?.data?.message ||
                        error.response?.data?.error ||
                        error.message ||
                        "Failed to cancel appointment";

    return {
      success: false,
      error: errorMessage,
      details: error.response?.data
    };
  }
};

// PATCH /api/appointments/{appointmentId}/confirm - Confirm appointment
export const confirmAppointment = async (
  appointmentId: string,
  notes?: string,
  notifyPatient: boolean = true
) => {
  try {
    console.log("Confirming appointment:", appointmentId, { notes, notifyPatient });

    const confirmDto: ConfirmAppointmentDto = {
      notes,
      notifyPatient
    };

    const response = await axios.patch<ApiResponseAppointmentResponseDTO>(
      `${BASE_URL}/api/appointments/${appointmentId}/confirm`,
      confirmDto
    );

    if (response.data.success && response.data.data) {
      const appointment = transformToAppointment(response.data.data);
      return {
        success: true,
        data: appointment,
        message: response.data.message || "Appointment confirmed successfully"
      };
    } else {
      return {
        success: false,
        error: response.data.error || "Failed to confirm appointment"
      };
    }
  } catch (error: any) {
    console.error("Confirm Appointment Error:", error.response?.data || error.message);

    const errorMessage = error.response?.data?.message ||
                        error.response?.data?.error ||
                        error.message ||
                        "Failed to confirm appointment";

    return {
      success: false,
      error: errorMessage,
      details: error.response?.data
    };
  }
};

// Utility functions for common appointment operations

// Get appointments for today
export const getTodayAppointments = async (facilityId?: string) => {
  const today = new Date().toISOString().split('T')[0];
  return getAppointments({
    facilityId,
    dateFrom: today,
    dateTo: today,
    page: 0,
    size: 100
  });
};

// Get upcoming appointments
export const getUpcomingAppointments = async (facilityId?: string, days: number = 7) => {
  const today = new Date();
  const futureDate = new Date(today.getTime() + (days * 24 * 60 * 60 * 1000));

  return getAppointments({
    facilityId,
    dateFrom: today.toISOString().split('T')[0],
    dateTo: futureDate.toISOString().split('T')[0],
    page: 0,
    size: 100
  });
};

// Get appointments by patient ID
export const getPatientAppointments = async (patientId: string, facilityId?: string) => {
  return getAppointments({
    patientId,
    facilityId,
    page: 0,
    size: 50
  });
};



// Check appointment availability (if this endpoint exists in the backend)
export const checkAppointmentAvailability = async (
  providerId: string,
  date: string,
  startTime: string,
  endTime: string
) => {
  try {
    console.log("Checking appointment availability:", { providerId, date, startTime, endTime });

    // This would need to be implemented in the backend
    const params = new URLSearchParams({
      providerId,
      date,
      startTime,
      endTime
    });

    const response = await axios.get(`${BASE_URL}/api/appointments/check-availability?${params}`);

    return {
      success: true,
      available: response.data.available || false,
      conflicts: response.data.conflicts || [],
      message: response.data.message || ""
    };
  } catch (error: any) {
    console.error("Failed to check availability:", error.response?.data || error.message);

    return {
      success: false,
      available: false,
      error: error.response?.data?.message || error.message
    };
  }
};

// Get appointment statistics (if this endpoint exists in the backend)
export const getAppointmentStats = async (facilityId?: string, dateFrom?: string, dateTo?: string): Promise<AppointmentStats> => {
  try {
    const params = new URLSearchParams();
    if (facilityId) params.append('facilityId', facilityId);
    if (dateFrom) params.append('dateFrom', dateFrom);
    if (dateTo) params.append('dateTo', dateTo);

    const response = await axios.get(`${BASE_URL}/api/appointments/stats?${params}`);

    // Check if response has the expected structure
    const responseData = response.data;

    // Handle wrapped response format {success: true, data: {...}}
    if (responseData && responseData.success && responseData.data) {
      console.log("API returned wrapped response, extracting data:", responseData.data);
      return responseData.data;
    }

    // Handle direct stats response
    if (responseData && typeof responseData === 'object' && responseData.totalAppointments !== undefined) {
      console.log("API returned direct stats response:", responseData);
      return responseData;
    }

    console.warn("Unexpected stats response structure:", responseData);
    throw new Error("Invalid stats response structure");
  } catch (error: any) {
    console.error("Failed to fetch appointment stats:", error.response?.data || error.message);

    // Return mock data as fallback for development
    return {
      totalAppointments: 150,
      todayAppointments: 12,
      upcomingAppointments: 45,
      completedAppointments: 89,
      cancelledAppointments: 6,
      noShowAppointments: 3,
      statusBreakdown: {
        Scheduled: 25,
        Confirmed: 20,
        InProgress: 5,
        Completed: 89,
        Cancelled: 6,
        NoShow: 3,
        Rescheduled: 2
      },
      typeBreakdown: {
        CONSULTATION: 80,
        FollowUp: 35,
        Emergency: 15,
        Procedure: 10,
        Surgery: 5,
        Diagnostic: 8,
        Vaccination: 12,
        Checkup: 20
      }
    };
  }
};

// Additional utility functions for provider and time slot management
// Note: These endpoints may not exist in the current backend and would need to be implemented

// Get provider schedule for a specific date
export const getProviderSchedule = async (providerId: string, date: string): Promise<ProviderSchedule> => {
  try {
    console.log("Fetching provider schedule:", providerId, date);

    const response = await axios.get(`${BASE_URL}/api/providers/${providerId}/schedule/${date}`);

    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch provider schedule:", error.response?.data || error.message);

    const errorMessage = error.response?.data?.message ||
                        error.response?.data?.error ||
                        "Failed to fetch provider schedule";

    throw new Error(errorMessage);
  }
};

// Get available time slots for a provider on a specific date
export const getAvailableSlots = async (providerId: string, date: string): Promise<TimeSlot[]> => {
  try {
    console.log("Fetching available slots:", providerId, date);

    const response = await axios.get(`${BASE_URL}/api/providers/${providerId}/available-slots/${date}`);

    const slotsData = response.data;

    // Ensure we return an array
    if (Array.isArray(slotsData)) {
      return slotsData;
    } else if (slotsData.slots) {
      return slotsData.slots;
    } else {
      return [];
    }
  } catch (error: any) {
    console.error("Failed to fetch available slots:", error.response?.data || error.message);

    // Return empty array on error instead of throwing
    return [];
  }
};
