import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Calendar, Clock, User, Save, X } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { Select } from '../../commonfields/Select';
import { Input } from '../../commonfields/Input';
import { Calendar as CalendarInput } from '../../commonfields/Calendar';
import { FormField } from '../../commonfields/FormField';
import FormMessage from '../../commonfields/FormMessage';
import { showSuccess, showError } from '../../utils/toastUtils';
import {
  createScheduleConfig,
  getScheduleConfigs,
  updateScheduleConfig,
  deleteScheduleConfig,
  getDoctors,
  type Doctor
} from '../../services/scheduleApis';
import type { DoctorSchedule, ScheduleConfigPayload } from '../../types/schedule';
import { DayOfWeek, dayOfWeekOptions, slotDurationOptions } from '../../types/appointmentenums';

interface DoctorScheduleAdminProps {
  facilityId?: string;
}

export const DoctorScheduleAdmin: React.FC<DoctorScheduleAdminProps> = ({ facilityId }) => {
  const [schedules, setSchedules] = useState<DoctorSchedule[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<DoctorSchedule | null>(null);
  const [formData, setFormData] = useState<ScheduleConfigPayload>({
    consultantId: '',
    daysOfWeek: [],
    startTime: '09:00:00',
    endTime: '17:00:00',
    slotDuration: 30,
    effectiveFrom: new Date().toISOString().split('T')[0],
    effectiveTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      // Load doctors/consultants
      const doctorsResponse = await getDoctors();
      if (doctorsResponse.success) {
        setDoctors(doctorsResponse.data || []);
      } else {
        showError(doctorsResponse.error || 'Failed to load doctors');
      }

      // Load schedules
      const schedulesResponse = await getScheduleConfigs({ size: 100 });
      setSchedules(schedulesResponse.results || []);
    } catch (error) {
      console.error('Failed to load initial data:', error);
      showError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // // Validate form
    // const validation = await validateScheduleConfig(formData);
    // if (!validation.isValid) {
    //   const errors: Record<string, string> = {};
    //   validation.errors.forEach((error, index) => {
    //     errors[`error_${index}`] = error;
    //   });
    //   setFormErrors(errors);
    //   return;
    // }

    setLoading(true);
    try {
      let result;
      if (editingSchedule) {
        // Use consultantId instead of scheduleId for the new API
        result = await updateScheduleConfig(editingSchedule.consultantId, formData);
      } else {
        result = await createScheduleConfig(formData);
      }

      if (result.success) {
        showSuccess(editingSchedule ? 'Schedule updated successfully' : 'Schedule created successfully');
        setShowForm(false);
        setEditingSchedule(null);
        resetForm();
        loadInitialData();
      } else {
        showError(result.error || 'Failed to save schedule');
      }
    } catch (error) {
      console.error('Failed to save schedule:', error);
      showError('Failed to save schedule');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (schedule: DoctorSchedule) => {
    setEditingSchedule(schedule);
    setFormData({
      consultantId: schedule.consultantId,
      daysOfWeek: schedule.daysOfWeek.map(day => day.toString()),
      startTime: schedule.startTime,
      endTime: schedule.endTime,
      slotDuration: schedule.slotDuration as number,
      effectiveFrom: schedule.effectiveFrom,
      effectiveTo: schedule.effectiveTo
    });
    setShowForm(true);
  };

  const handleDelete = async (scheduleId: string) => {
    if (!confirm('Are you sure you want to delete this schedule?')) return;

    setLoading(true);
    try {
      const result = await deleteScheduleConfig(scheduleId);
      if (result.success) {
        showSuccess('Schedule deleted successfully');
        loadInitialData();
      } else {
        showError(result.error || 'Failed to delete schedule');
      }
    } catch (error) {
      console.error('Failed to delete schedule:', error);
      showError('Failed to delete schedule');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      consultantId: '',
      daysOfWeek: [],
      startTime: '09:00:00',
      endTime: '17:00:00',
      slotDuration: 30,
      effectiveFrom: new Date().toISOString().split('T')[0],
      effectiveTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    });
    setFormErrors({});
  };

  const handleDayOfWeekChange = (day: string, checked: boolean) => {
    if (checked) {
      setFormData(prev => ({
        ...prev,
        daysOfWeek: [...prev.daysOfWeek, day]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        daysOfWeek: prev.daysOfWeek.filter(d => d !== day)
      }));
    }
  };

  const handleSelectAllDays = () => {
    setFormData(prev => ({
      ...prev,
      daysOfWeek: dayOfWeekOptions.map(day => day.toUpperCase())
    }));
  };

  const handleDeselectAllDays = () => {
    setFormData(prev => ({
      ...prev,
      daysOfWeek: []
    }));
  };

  const isAllDaysSelected = dayOfWeekOptions.every(day =>
    formData.daysOfWeek.includes(day.toUpperCase())
  );

  const getDoctorName = (doctorId: string) => {
    const doctor = doctors.find(d => d.doctorId === doctorId);
    return doctor ? doctor.fullName : doctorId;
  };

  const getDoctorStatus = (doctorId: string) => {
    const doctor = doctors.find(d => d.doctorId === doctorId);
    return doctor ? doctor.isActive : false;
  };



  const formatDaysOfWeek = (days: DayOfWeek[]) => {
    return days.map(day => day.toString()).join(', ');
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Doctor Schedule Management</h1>
          <p className="text-gray-600">Configure doctor schedules and working hours</p>
        </div>
        <Button
          onClick={() => {
            setEditingSchedule(null);
            resetForm();
            setShowForm(true);
          }}
          className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          <Plus size={16} />
          <span>New Schedule</span>
        </Button>
      </div>

      {/* Schedule Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 blur-backdrop flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-gray-800">
                  {editingSchedule ? 'Edit Schedule' : 'Create New Schedule'}
                </h2>
                <Button
                  onClick={() => {
                    setShowForm(false);
                    setEditingSchedule(null);
                    resetForm();
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600"
                >
                  <X size={20} />
                </Button>
              </div>
            </div>

            <form onSubmit={handleFormSubmit} className="p-6 space-y-6">
              {/* Doctor/Consultant Selection */}
              <FormField label="Doctor/Consultant" required>
                <Select
                  value={formData.consultantId}
                  onChange={(e) => setFormData(prev => ({ ...prev, consultantId: e.target.value }))}
                  required
                >
                  <option value="">Select Doctor/Consultant</option>
                  {doctors.map((doctor) => (
                    <option key={doctor.doctorId} value={doctor.doctorId}>
                      {doctor.fullName} - {doctor.specialization?.name || 'No specialization'} ({doctor.registrationNumber || 'No registration'})
                    </option>
                  ))}
                </Select>
                {formErrors.consultantId && <FormMessage>{formErrors.consultantId}</FormMessage>}
              </FormField>

              {/* Days of Week */}
              <FormField label="Days of Week" required>
                {/* Select All / Deselect All Buttons */}
                <div className="flex items-center space-x-3 mb-3">
                  <Button
                    type="button"
                    onClick={handleSelectAllDays}
                    disabled={isAllDaysSelected}
                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    Select All Days
                  </Button>
                  <Button
                    type="button"
                    onClick={handleDeselectAllDays}
                    disabled={formData.daysOfWeek.length === 0}
                    className="px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    Deselect All
                  </Button>
                  <span className="text-xs text-gray-500">
                    ({formData.daysOfWeek.length} of {dayOfWeekOptions.length} selected)
                  </span>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {dayOfWeekOptions.map((day) => (
                    <label key={day} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.daysOfWeek.includes(day.toUpperCase())}
                        onChange={(e) => handleDayOfWeekChange(day.toUpperCase(), e.target.checked)}
                        className="form-checkbox h-4 w-4 text-indigo-600"
                      />
                      <span className="text-sm text-gray-700">{day}</span>
                    </label>
                  ))}
                </div>
                {formErrors.daysOfWeek && <FormMessage>{formErrors.daysOfWeek}</FormMessage>}
              </FormField>

              {/* Time Configuration */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField label="Start Time" required>
                  <Input
                    type="time"
                    value={formData.startTime.substring(0, 5)}
                    onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value + ':00' }))}
                    required
                  />
                  {formErrors.startTime && <FormMessage>{formErrors.startTime}</FormMessage>}
                </FormField>

                <FormField label="End Time" required>
                  <Input
                    type="time"
                    value={formData.endTime.substring(0, 5)}
                    onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value + ':00' }))}
                    required
                  />
                  {formErrors.endTime && <FormMessage>{formErrors.endTime}</FormMessage>}
                </FormField>
              </div>

              {/* Slot Duration */}
              <FormField label="Slot Duration" required>
                <Select
                  value={formData.slotDuration.toString()}
                  onChange={(e) => setFormData(prev => ({ ...prev, slotDuration: parseInt(e.target.value) }))}
                  required
                >
                  {slotDurationOptions.map((duration) => (
                    <option key={duration} value={duration}>
                      {duration} minutes
                    </option>
                  ))}
                </Select>
                {formErrors.slotDuration && <FormMessage>{formErrors.slotDuration}</FormMessage>}
              </FormField>

              {/* Effective Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField label="Effective From" required>
                  <CalendarInput
                    value={formData.effectiveFrom}
                    onChange={(e) => setFormData(prev => ({ ...prev, effectiveFrom: e.target.value }))}
                    required
                  />
                  {formErrors.effectiveFrom && <FormMessage>{formErrors.effectiveFrom}</FormMessage>}
                </FormField>

                <FormField label="Effective To" required>
                  <CalendarInput
                    value={formData.effectiveTo}
                    onChange={(e) => setFormData(prev => ({ ...prev, effectiveTo: e.target.value }))}
                    required
                  />
                  {formErrors.effectiveTo && <FormMessage>{formErrors.effectiveTo}</FormMessage>}
                </FormField>
              </div>

              {/* Form Errors */}
              {Object.keys(formErrors).length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="text-red-800 font-medium mb-2">Please fix the following errors:</h4>
                  <ul className="text-red-700 text-sm space-y-1">
                    {Object.values(formErrors).map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Form Actions */}
              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  onClick={() => {
                    setShowForm(false);
                    setEditingSchedule(null);
                    resetForm();
                  }}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
                >
                  {loading ? 'Saving...' : editingSchedule ? 'Update Schedule' : 'Create Schedule'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Schedules Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800">Existing Schedules</h2>
        </div>
        
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading schedules...</p>
          </div>
        ) : schedules.length === 0 ? (
          <div className="p-6 text-center">
            <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-600 mb-2">No Schedules Found</h3>
            <p className="text-gray-500">Create your first doctor schedule to get started.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Provider
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Days
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Effective Period
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Doctor Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {schedules.map((schedule) => (
                  <tr key={schedule.scheduleId} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <User className="h-5 w-5 text-gray-400 mr-2" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {getDoctorName(schedule.consultantId)}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDaysOfWeek(schedule.daysOfWeek)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-gray-400 mr-1" />
                        {schedule.startTime.substring(0, 5)} - {schedule.endTime.substring(0, 5)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {schedule.slotDuration} min
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {schedule.effectiveFrom} to {schedule.effectiveTo}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        getDoctorStatus(schedule.consultantId)
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {getDoctorStatus(schedule.consultantId) ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Button
                          onClick={() => handleEdit(schedule)}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          <Edit size={16} />
                        </Button>
                        <Button
                          onClick={() => handleDelete(schedule.scheduleId!)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 size={16} />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};
