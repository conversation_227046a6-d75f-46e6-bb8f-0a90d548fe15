import React, { useState } from "react";
import AbhaInput from "../../verification/AbhaInput";
import AadhaarInput from "../../verification/AadhaarInput";
import PanInput from "../../verification/PanInput";
import GenerateAbhaCardPopup from "../../verification/GenerateAbhaCardPopup";

type Props = {
    isEditMode: boolean;
};

export const PatientVerificationSection: React.FC<Props> = ({ isEditMode }) => {
    const [abhaNumber, setAbhaNumber] = useState("");
    const [aadhaarNumber, setAadhaarNumber] = useState("");
    const [panNumber, setPanNumber] = useState("");
    const [mode, setMode] = useState<"verify" | "generate">("verify");
    const [selectedMethod, setSelectedMethod] = useState<"aadhaar" | "pan" | "mobile">("aadhaar");
    const [showAbhaModal, setShowAbhaModal] = useState(false);
    const [mobile, setMobile] = useState("");
    const [showPopup, setShowPopup] = useState(false);

    const handleSendOtp = () => {
        setShowPopup(true);
    };


    if (isEditMode) return null;

    return (
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden mb-2">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-2 py-2 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                    <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-indigo-600 rounded-full"></div>
                    <h3 className="text-lg font-semibold text-gray-800">
                        {mode === "verify" ? "Verify ABHA" : "Create ABHA"}
                    </h3>
                </div>
            </div>

            <div className="p-2">
                {mode === "verify" ? (
                    <>
                        <div className="mb-4">
                            <label className="block text-sm font-medium mb-1 text-gray-700">Verify ABHA using ABHA NUMBER or MOBILE NUMBER</label>
                            <AbhaInput abhaNumber={abhaNumber} setAbhaNumber={setAbhaNumber} />
                            {/* <p className="mt-2 text-sm text-gray-600">
                                Use ABHA Number to verify existing patient records.
                            </p> */}
                        </div>
                        <button
                            onClick={() => setMode("generate")}
                            className="text-sm font-semibold text-blue-600 underline hover:text-blue-800 transition cursor-pointer hover:underline hover:scale-105"
                        >
                            Don’t have an ABHA Number? Generate one
                        </button>
                    </>
                ) : (
                    <>
                        <h4 className="text-md font-medium text-gray-800 mb-4">Generate ABHA Number Using:</h4>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                            <div className="space-y-2">
                                <label className="block text-xs font-medium text-gray-700 mb-1">Select Method</label>
                                <div className="relative w-full">
                                    <select
                                        value={selectedMethod}
                                        onChange={(e) => setSelectedMethod(e.target.value as any)}
                                        className="w-full h-10 border border-gray-300 rounded-md px-3 py-2 text-sm bg-white text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none pr-10"
                                    >
                                        <option value="aadhaar">Aadhaar</option>
                                        <option value="mobile">Generate ABHA Card using Mobile</option>
                                        <option value="pan">Driving License (Coming Soon)</option>
                                    </select>
                                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                        <svg
                                            className="w-4 h-4 text-gray-500"
                                            xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20"
                                            fill="currentColor"
                                        >
                                            <path fillRule="evenodd" d="M10 12a1 1 0 01-.707-.293l-3-3a1 1 0 111.414-1.414L10 9.586l2.293-2.293a1 1 0 011.414 1.414l-3 3A1 1 0 0110 12z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                </div>

                            </div>

                            {/* Aadhaar Input */}
                            {selectedMethod === "aadhaar" && (
                                <div className="space-y-2">
                                    <label className="block text-xs font-medium text-gray-700 mb-1">Aadhaar Details</label>
                                    <AadhaarInput
                                        aadhaarNumber={aadhaarNumber}
                                        setAadhaarNumber={setAadhaarNumber}
                                    />
                                    <p className="mt-2 text-sm text-gray-600">
                                        Use Aadhaar to generate OTP and create a new ABHA profile.
                                    </p>
                                </div>
                            )}

                            {/* PAN Input */}
                            {selectedMethod === "pan" && (
                                <div className="space-y-2">
                                    <label className="block text-xs font-medium text-gray-700 mb-1">Enter Driving License Number</label>
                                    <PanInput
                                        panNumber={panNumber}
                                        setPanNumber={setPanNumber}
                                    />
                                    <p className="mt-2 text-sm text-gray-600">
                                        Use PAN to generate OTP and create a new ABHA profile.
                                    </p>
                                </div>
                            )}

                            {selectedMethod === "mobile" && (
                                <>
                                    <div className="flex flex-col md:flex-row items-center gap-4 mt-2">
                                        <div className="w-full md:w-1/2">
                                            <label className="block text-xs font-medium text-gray-700 mb-1">Enter Mobile Number</label>
                                            <input
                                                type="text"
                                                value={mobile}
                                                onChange={(e) => setMobile(e.target.value.replace(/\D/g, "").slice(0, 10))}
                                                placeholder="Enter 10-digit mobile"
                                                className="w-full h-10 border border-gray-300 rounded-md px-3 py-2 text-sm"
                                            />
                                        </div>
                                        <button
                                            type="button"
                                            onClick={handleSendOtp}
                                            disabled={mobile.length !== 10}
                                            className="mt-6 md:mt-0 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition disabled:opacity-50"
                                        >
                                            Send OTP
                                        </button>
                                    </div>

                                    <GenerateAbhaCardPopup
                                        isOpen={showPopup}
                                        onClose={() => setShowPopup(false)}
                                        mobile={mobile}
                                    />
                                </>
                            )}


                        </div>

                        <button
                            onClick={() => setMode("verify")}
                            className="text-sm font-semibold text-blue-600 underline hover:text-blue-800 transition cursor-pointer hover:underline hover:scale-105"
                        >
                            Already have an ABHA Number? Verify here
                        </button>
                    </>
                )}
            </div>
        </div>
    );
};
