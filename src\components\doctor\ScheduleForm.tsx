import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  getDoctors,
  getScheduleConfigs,
  updateScheduleConfig
} from "../../services/scheduleApis";
import { Button } from "../../commonfields/Button";
import { Input } from "../../commonfields/Input";
import { Select } from "../../commonfields/Select";
import { Calendar as CalendarInput } from "../../commonfields/Calendar";
import { FormField } from "../../commonfields/FormField";
import { showSuccess, showError } from "../../utils/toastUtils";
import { X } from "lucide-react";
import { dayOfWeekOptions, slotDurationOptions } from "../../types/appointmentenums";
import type { DoctorSchedule, ScheduleConfigPayload } from "../../types/schedule";
import type { Doctor } from "../../services/scheduleApis";

const ScheduleForm: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const doctorId = searchParams.get("doctorId");

const [formData, setFormData] = useState<ScheduleConfigPayload>({
  consultantId: doctorId || "", // ✅ include consultantId here
  daysOfWeek: [],
  startTime: "09:00:00",
  endTime: "17:00:00",
  slotDuration: 30,
  effectiveFrom: new Date().toISOString().split("T")[0],
  effectiveTo: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]
});


  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!doctorId) {
      showError("No doctor ID provided");
      navigate("/doctors");
      return;
    }

    const fetchInitialData = async () => {
      try {
        const [doctorRes, scheduleRes] = await Promise.all([
          getDoctors(),
          getScheduleConfigs({ consultantId: doctorId })
        ]);

        if (doctorRes.success && doctorRes.data) {
          setDoctors(doctorRes.data);
        }

     const schedule = scheduleRes.results.find(s => s.consultantId === doctorId);
if (schedule) {
  setFormData({
    consultantId: schedule.consultantId,
    daysOfWeek: schedule.daysOfWeek || [],
    startTime: schedule.startTime,
    endTime: schedule.endTime,
    slotDuration: schedule.slotDuration,
    effectiveFrom: schedule.effectiveFrom,
    effectiveTo: schedule.effectiveTo
  });
}
 else {
          showError("No existing schedule found.");
        }
      } catch (error) {
        console.error(error);
        showError("Failed to load doctor or schedule data.");
      }
    };

    fetchInitialData();
  }, [doctorId, navigate]);

  const handleDayToggle = (day: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      daysOfWeek: checked
        ? [...prev.daysOfWeek, day]
        : prev.daysOfWeek.filter(d => d !== day)
    }));
  };

  const handleSelectAllDays = () => {
    setFormData(prev => ({
      ...prev,
      daysOfWeek: dayOfWeekOptions.map(day => day.toUpperCase())
    }));
  };

  const handleDeselectAllDays = () => {
    setFormData(prev => ({
      ...prev,
      daysOfWeek: []
    }));
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (!doctorId) {
      showError("Missing doctorId");
      setLoading(false);
      return;
    }

    if (formData.daysOfWeek.length === 0) {
      showError("Select at least one day of the week.");
      setLoading(false);
      return;
    }

    try {
      const res = await updateScheduleConfig(doctorId, formData);
      if (res.success) {
        showSuccess("Schedule updated successfully");
        navigate("/doctors");
      } else {
        showError(res.error || "Failed to update schedule");
      }
    } catch (err) {
      console.error(err);
      showError("An error occurred while updating schedule");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Edit Schedule</h2>
          <Button onClick={() => navigate("/doctors")} className="p-2 text-gray-400 hover:text-gray-600">
            <X size={20} />
          </Button>
        </div>

        <form onSubmit={onSubmit} className="space-y-6">
          <FormField label="Doctor/Consultant" required>
            <Select value={doctorId || ""} disabled>
              {doctors.map(doc => (
                <option key={doc.doctorId} value={doc.doctorId}>
                  {doc.fullName}
                </option>
              ))}
            </Select>
          </FormField>

          <FormField label="Days of Week" required>
            <div className="flex gap-3 mb-3">
              <Button type="button" onClick={handleSelectAllDays} className="bg-indigo-600 text-white px-3 py-1 text-sm rounded">Select All</Button>
              <Button type="button" onClick={handleDeselectAllDays} className="bg-gray-500 text-white px-3 py-1 text-sm rounded">Deselect All</Button>
              <span className="text-sm text-gray-500">{formData.daysOfWeek.length} of 7 selected</span>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {dayOfWeekOptions.map(day => (
                <label key={day} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.daysOfWeek.includes(day.toUpperCase())}
                    onChange={e => handleDayToggle(day.toUpperCase(), e.target.checked)}
                    className="form-checkbox h-4 w-4"
                  />
                  {day}
                </label>
              ))}
            </div>
          </FormField>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField label="Start Time" required>
              <Input
                type="time"
                value={formData.startTime.slice(0, 5)}
                onChange={e => setFormData(prev => ({ ...prev, startTime: `${e.target.value}:00` }))}
              />
            </FormField>
            <FormField label="End Time" required>
              <Input
                type="time"
                value={formData.endTime.slice(0, 5)}
                onChange={e => setFormData(prev => ({ ...prev, endTime: `${e.target.value}:00` }))}
              />
            </FormField>
          </div>

          <FormField label="Slot Duration" required>
            <Select
              value={formData.slotDuration.toString()}
              onChange={e => setFormData(prev => ({ ...prev, slotDuration: parseInt(e.target.value) }))}
            >
              {slotDurationOptions.map(d => (
                <option key={d} value={d}>{d} minutes</option>
              ))}
            </Select>
          </FormField>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField label="Effective From" required>
              <CalendarInput
                value={formData.effectiveFrom}
                onChange={e => setFormData(prev => ({ ...prev, effectiveFrom: e.target.value }))}
              />
            </FormField>
            <FormField label="Effective To" required>
              <CalendarInput
                value={formData.effectiveTo}
                onChange={e => setFormData(prev => ({ ...prev, effectiveTo: e.target.value }))}
              />
            </FormField>
          </div>

          <div className="flex justify-end gap-4">
            <Button type="button" onClick={() => navigate("/doctors")} className="bg-gray-300 px-5 py-2 rounded">Cancel</Button>
            <Button type="submit" disabled={loading} className="bg-indigo-600 text-white px-6 py-2 rounded hover:bg-indigo-700">
              {loading ? "Updating..." : "Update Schedule"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ScheduleForm;
