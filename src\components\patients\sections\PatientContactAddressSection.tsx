import React from "react";
import { FormField } from "../../../commonfields/FormField";
import { Input } from "../../../commonfields/Input";
import { Select } from "../../../commonfields/Select";
import FormMessage from "../../../commonfields/FormMessage";
import {
    contactModeOptions,
    phonePrefOptions,
    addressTypeOptions,
} from "../../../types/patientenums";
import type { PatientRegistrationPayload } from "../../../types/patient";
import { showError } from "../../../utils/toastUtils";
import { MobileSMSOtp } from '../../twilio/MobileSMSOtp'
import { allowOnlyNumbersWithLimit } from "../../../inputhelpers/inputHelpers";
import { allowOnlyLetters } from "../../../inputhelpers/inputHelpers";

type Props = {
    form: PatientRegistrationPayload;
    setForm: React.Dispatch<React.SetStateAction<PatientRegistrationPayload>>;
    formErrors: Record<string, string>;
    onFieldChange: (fieldPath: string) => (e: React.ChangeEvent<any>) => void;
    onAddressTypeChange: (index: number) => (e: React.ChangeEvent<HTMLSelectElement>) => void;
    addAddress: () => void;
    onCheckboxChange: (index: number) => void;
    presentSameAsPermanent: boolean;
};

export const PatientContactAddressSection: React.FC<Props> = ({
    form,
    setForm,
    formErrors,
    onFieldChange,
    onAddressTypeChange,
    addAddress,
    onCheckboxChange,
    presentSameAsPermanent,
}) => {
    return (
        <div className="space-y-8">
            {/* Contact Section */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-sky-50 to-blue-50 px-2 py-2 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-8 bg-gradient-to-b from-sky-500 to-blue-600 rounded-full"></div>
                            <h3 className="text-lg font-semibold text-gray-800">Contact Information</h3>
                        </div>
                        {
                            (form.contacts?.length || 0) < 3 && (
                                <button
                                    type="button"
                                    className="inline-flex items-center px-2 py-2 text-sm font-medium text-white bg-gradient-to-r from-sky-500 to-blue-600 rounded-lg hover:scale-105 transition-all"
                                    onClick={() => {
                                        setForm(prev => ({
                                            ...prev,
                                            contacts: [...(prev.contacts || []), {}],
                                        }));
                                    }}
                                >
                                    + Add Contact
                                </button>
                            )
                        }
                    </div>
                </div>

                <div className="space-y-4">
                    {form.contacts?.map((contact, index) => {
                        const isPrimary = index === 0;

                        return (
                            <div key={index} className="bg-gray-50 rounded-lg p-2 border border-gray-200 hover:shadow-md transition-shadow duration-200 relative">
                                <div className={`grid gap-4 ${isPrimary ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4' : 'grid-cols-1 sm:grid-cols-2 md:grid-cols-2'}`}>                                    <FormField
                                    label={isPrimary ? "Primary Phone Number" : `Alternative Number ${(index - 1) * 2 + 1}`}
                                    required={isPrimary}
                                >
                                    {isPrimary ? (
                                        <>
                                            <MobileSMSOtp
                                                phoneNumber={contact.phoneNumber || ""}
                                                onChange={(value) => {
                                                    // Step 1: Update the form state
                                                    setForm((prev) => {
                                                        const updated = [...(prev.contacts || [])];
                                                        updated[0] = { ...updated[0], phoneNumber: value };
                                                        return { ...prev, contacts: updated };
                                                    });

                                                    // ✅ Step 2: Trigger validation manually to restore live error display
                                                    onFieldChange("contacts.0.phoneNumber")({
                                                        target: { value },
                                                    } as React.ChangeEvent<HTMLInputElement>);
                                                }}
                                            />

                                            {/* ✅ Ensure the validation message renders like it used to */}
                                            <FormMessage>{formErrors?.[`contacts.0.phoneNumber`]}</FormMessage>
                                        </>
                                    ) : (
                                        <>
                                            <Input
                                                name="phoneNumber"
                                                value={contact.phoneNumber || ""}
                                                onChange={onFieldChange(`contacts.${index}.phoneNumber`)}
                                                onKeyDown={allowOnlyNumbersWithLimit(10)}
                                                className="w-full px-2 py-2 border border-gray-300 rounded-lg"
                                            />
                                            <FormMessage>{formErrors?.[`contacts.${index}.phoneNumber`]}</FormMessage>
                                        </>
                                    )}
                                </FormField>



                                    {isPrimary ? (
                                        <>
                                            <FormField label="Email">
                                                <Input
                                                    name="email"
                                                    value={contact.email || ""}
                                                    onChange={onFieldChange(`contacts.${index}.email`)}
                                                    className="w-full px-2 py-2 border border-gray-300 rounded-lg"
                                                />
                                                <FormMessage>{formErrors?.[`contacts.${index}.email`]}</FormMessage>
                                            </FormField>

                                            <FormField label="Preferred Contact Mode">
                                                <Select
                                                    name="preferredContactMode"
                                                    value={contact.preferredContactMode || ""}
                                                    onChange={onFieldChange(`contacts.${index}.preferredContactMode`)}
                                                    className="w-full px-2 py-2 border border-gray-300 rounded-lg"
                                                >
                                                    <option value="">Select</option>
                                                    {contactModeOptions.map(mode => (
                                                        <option key={mode} value={mode}>{mode}</option>
                                                    ))}
                                                </Select>
                                                <FormMessage>{formErrors?.[`contacts.${index}.preferredContactMode`]}</FormMessage>
                                            </FormField>

                                            {contact.preferredContactMode === "Phone" && (
                                                <FormField label="Phone Contact Preference">
                                                    <Select
                                                        name="phoneContactPreference"
                                                        value={contact.phoneContactPreference || ""}
                                                        onChange={onFieldChange(`contacts.${index}.phoneContactPreference`)}
                                                        className="w-full px-2 py-2 border border-gray-300 rounded-lg"
                                                    >
                                                        <option value="">Select</option>
                                                        {phonePrefOptions.map(pref => (
                                                            <option key={pref} value={pref}>{pref}</option>
                                                        ))}
                                                    </Select>
                                                    <FormMessage>{formErrors?.[`contacts.${index}.phoneContactPreference`]}</FormMessage>
                                                </FormField>
                                            )}

                                            <FormField label="Consent to Share">
                                                <div className="flex items-center space-x-2 mt-2">
                                                    <input
                                                        type="checkbox"
                                                        id={`consent-${index}`}
                                                        name="consentToShare"
                                                        checked={contact.consentToShare === true}
                                                        onChange={(e) => {
                                                            const value = e.target.checked;
                                                            setForm(prev => {
                                                                const updated = [...(prev.contacts || [])];
                                                                updated[index] = {
                                                                    ...updated[index],
                                                                    consentToShare: value,
                                                                };
                                                                return { ...prev, contacts: updated };
                                                            });
                                                        }}
                                                        className="rounded border-gray-300 text-indigo-600"
                                                    />
                                                    <label htmlFor={`consent-${index}`} className="text-sm text-gray-700">
                                                        Yes, I consent to share
                                                    </label>
                                                </div>
                                            </FormField>
                                        </>
                                    ) : (
                                        <FormField label={`Alternate Number ${(index - 1) * 2 + 2}`}>
                                            <Input
                                                name="mobileNumber"
                                                value={contact.mobileNumber || ""}
                                                onChange={onFieldChange(`contacts.${index}.mobileNumber`)}
                                                onKeyDown={allowOnlyNumbersWithLimit(10)}
                                                className="w-full px-2 py-2 border border-gray-300 rounded-lg"
                                            />
                                            <FormMessage>{formErrors?.[`contacts.${index}.mobileNumber`]}</FormMessage>
                                        </FormField>
                                    )}
                                </div>

                                {!isPrimary && (
                                    <div className="flex justify-end mt-4">
                                        <button
                                            type="button"
                                            className="text-red-600 bg-red-50 border border-red-200 rounded-lg px-2 py-2 hover:bg-red-100 text-sm"
                                            onClick={() => {
                                                setForm(prev => ({
                                                    ...prev,
                                                    contacts: prev.contacts?.filter((_, i) => i !== index),
                                                }));
                                            }}
                                        >
                                            Remove
                                        </button>
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>
            </div>

            {/* Address Section */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-amber-50 to-orange-50 px-2 py-2 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-8 bg-gradient-to-b from-amber-500 to-orange-600 rounded-full"></div>
                            <h3 className="text-lg font-semibold text-gray-800">Address Information</h3>
                        </div>
                        <button
                            type="button"
                            className="inline-flex items-center px-2 py-2 text-sm font-medium text-white bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg hover:scale-105"
                            onClick={addAddress}
                        >
                            + Add Address
                        </button>
                    </div>
                </div>

                <div className="p-2 space-y-4">
                    {form.addresses?.map((address, index) => {
                        const isPresent = address.addressType === "Present";
                        const permanentExists = form.addresses.some((a, i) => a.addressType === "Permanent" && i !== index);

                        return (
                            <div key={index} className="bg-gray-50 rounded-lg p-2 border border-gray-200">
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">                                    {[
                                    { label: "Address Type", name: "addressType", type: "select", options: addressTypeOptions },
                                    { label: "House/Flat No", name: "houseNoOrFlatNo" },
                                    { label: "Locality/Sector", name: "localityOrSector" },
                                    { label: "City/Village", name: "cityOrVillage" },
                                    { label: "Pincode", name: "pincode" },
                                    { label: "District", name: "districtId" },
                                    { label: "State", name: "stateId" },
                                    { label: "Country", name: "country" },
                                ].map(({ label, name, type, options }) => (
                                    <FormField key={name} label={label}>
                                        {type === "select" ? (
                                            <Select
                                                name={name}
                                                value={address[name] || ""}
                                                onChange={
                                                    name === "addressType"
                                                        ? onAddressTypeChange(index)
                                                        : onFieldChange(`addresses.${index}.${name}`)
                                                }
                                                disabled={presentSameAsPermanent && isPresent}
                                                className="w-full"
                                            >
                                                <option value="">Select</option>
                                                {options?.map((opt) => (
                                                    <option key={opt} value={opt}>{opt}</option>
                                                ))}
                                            </Select>
                                        ) : (
                                            <Input
                                                name={name}
                                                value={address[name] || ""}
                                                onChange={onFieldChange(`addresses.${index}.${name}`)}
                                                onKeyDown={
                                                    name === "pincode"
                                                        ? allowOnlyNumbersWithLimit(6)
                                                        : ["cityOrVillage", "districtId", "stateId", "country"].includes(name)
                                                            ? allowOnlyLetters(15)
                                                            : undefined
                                                }
                                                disabled={presentSameAsPermanent && isPresent}
                                                className="w-full"
                                            />
                                        )}
                                        <FormMessage>{formErrors?.[`addresses.${index}.${name}`]}</FormMessage>
                                    </FormField>
                                ))}
                                </div>

                                {isPresent && permanentExists && (
                                    <div className="mt-4 flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id={`presentSameAsPermanent-${index}`}
                                            checked={presentSameAsPermanent}
                                            onChange={() => onCheckboxChange(index)}
                                        />
                                        <label htmlFor={`presentSameAsPermanent-${index}`} className="text-sm text-gray-700">
                                            Present address same as Permanent
                                        </label>
                                    </div>
                                )}

                                {index > 0 && (
                                    <div className="flex justify-end mt-4">
                                        <button
                                            type="button"
                                            className="text-red-600 bg-red-50 border border-red-200 rounded-lg px-2 py-2 hover:bg-red-100 text-sm"
                                            onClick={() => {
                                                if ((form.addresses?.length || 0) <= 1) {
                                                    showError("Cannot remove", "At least one address must remain.");
                                                    return;
                                                }
                                                setForm(prev => ({
                                                    ...prev,
                                                    addresses: prev.addresses?.filter((_, i) => i !== index),
                                                }));
                                            }}
                                            disabled={presentSameAsPermanent && isPresent}
                                        >
                                            Remove
                                        </button>
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};
