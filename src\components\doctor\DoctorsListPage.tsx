// Updated imports
import React, { useEffect, useState, useRef } from "react";
import {
  <PERSON>aS<PERSON>ch, FaUserMd, FaTrash, FaEye, FaEdit, FaCalendarAlt, FaEllipsisV, FaCog, FaBan
} from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { getAllDoctors, deleteDoctor } from "../../services/doctorApis";
import DoctorProfileModal from "./DoctorProfileModal";
import ConfirmDialog from "../../utils/ConfirmDialog";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { getScheduleConfigs } from "../../services/scheduleApis";
import { deleteConfigsByConsultantId } from "../../services/scheduleApis"; // adjust import path



interface Doctor {
  doctorId: string;
  fullName: string;
  roleType: string;
  specialization: string;
  gender: string;
  dateOfBirth: string;
  mobileNumber: string;
  email: string;
  registrationNumber: string;
  registrationState: string;
  yearsOfExperience: number;
  telemedicineReady: boolean;
  languagesSpoken: string;
  isActive: boolean;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  createdAt: string;
}

const DoctorListPage = () => {
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(true);
  const [selectedDoctorId, setSelectedDoctorId] = useState<string | null>(null);
  const [confirmDeleteId, setConfirmDeleteId] = useState<string | null>(null);
  const navigate = useNavigate();
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [doctorsWithSchedule, setDoctorsWithSchedule] = useState<Record<string, boolean>>({});
  const [confirmDelete, setConfirmDelete] = useState<null | string>(null);
const [isDeleting, setIsDeleting] = useState(false);


  useEffect(() => {
  const fetchData = async () => {
    try {
      const docRes = await getAllDoctors();
      if (!docRes.success) throw new Error("Failed to load Practitioner");

      const transformedDoctors: Doctor[] = docRes.data.map((doc: any): Doctor => {
        const name = [doc.firstName, doc.middleName, doc.lastName].filter(Boolean).join(" ");
        const fullName = doc.roleType === "Doctor" ? `Dr. ${name}` : name;
        return {
          ...doc,
          fullName,
          doctorId: doc.doctorId,
        };
      });

      setDoctors(transformedDoctors);

      const scheduleResults = await Promise.all(
        transformedDoctors.map(async (doc) => {
          try {
            const res = await getScheduleConfigs({ consultantId: doc.doctorId });

            const today = new Date();
            const schedules = Array.isArray(res.results) ? res.results : [];

            const hasActiveSchedule = schedules.some((schedule: any) => {
              const fromStr = schedule.effectiveFrom;
              const toStr = schedule.effectiveTo;
              if (!fromStr || !toStr) return false;

              const from = new Date(fromStr);
              const to = new Date(toStr);

              return (
                from instanceof Date &&
                to instanceof Date &&
                !isNaN(from.getTime()) &&
                !isNaN(to.getTime()) &&
                from <= today && today <= to
              );
            });

            return { doctorId: doc.doctorId, hasSchedule: hasActiveSchedule };
          } catch (e) {
            console.error(`Schedule check failed for doctor ${doc.doctorId}`, e);
            return { doctorId: doc.doctorId, hasSchedule: false };
          }
        })
      );

      const scheduleMap: Record<string, boolean> = {};
      scheduleResults.forEach(({ doctorId, hasSchedule }) => {
        scheduleMap[doctorId] = hasSchedule;
      });

      setDoctorsWithSchedule(scheduleMap);
      console.log("✅ doctorsWithSchedule map:", scheduleMap);

    } catch (err) {
      console.error(err);
      toast.error("Unexpected error occurred.");
    } finally {
      setLoading(false);
    }
  };

  fetchData();
}, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      if (
        openDropdown &&
        !document.getElementById(`dropdown-btn-${openDropdown}`)?.contains(target) &&
        !document.getElementById(`dropdown-menu-${openDropdown}`)?.contains(target)
      ) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openDropdown]);


  const filteredDoctors = doctors.filter(doc =>
    doc.fullName.toLowerCase().includes(search.toLowerCase()) ||
    doc.registrationNumber.toLowerCase().includes(search.toLowerCase())
  );

  const handleDelete = async () => {
    if (!confirmDeleteId) return;
    const res = await deleteDoctor(confirmDeleteId);
    if (res.success) {
      setDoctors(prev => prev.filter(d => d.doctorId !== confirmDeleteId));
      toast.success("Practitioner deactiavted.");
    } else {
      toast.error("Delete failed.");
    }
    setConfirmDeleteId(null);
  };
const handleConfirmDelete = async () => {
  if (!confirmDelete) return;

  setIsDeleting(true);
  const result = await deleteConfigsByConsultantId(confirmDelete);
  setIsDeleting(false);

  if (result.success) {
    toast.success("Schedule deleted successfully");
  } else if (result.status === 404) {
    toast.info("No schedule found for this practitioner.");
  } else {
    toast.error(result.error || "Failed to delete schedule");
  }

  setConfirmDelete(null); // Close the dialog
};

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 font-sans">
      <ToastContainer />
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header and Buttons */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-pink-500">
              User Management
            </h1>
            <p className="text-gray-600 text-lg">
              Manage Hospital Users and Practitioner schedules
            </p>
          </div>
          <div className="flex space-x-4">
            <button
              onClick={() => navigate("/doctors/add")}
              className="bg-gradient-to-r from-indigo-600 to-pink-500 text-white px-6 py-3 rounded-xl hover:scale-105 transition-all shadow-lg"
            >
              <FaUserMd className="inline mr-2" /> Add User
            </button>
            <button
              onClick={() => navigate("/schedules")}
              className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-xl hover:scale-105 transition-all shadow-lg"
            >
              <FaCalendarAlt className="inline mr-2" /> Manage Schedules
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="bg-white rounded-xl shadow p-4 mb-6">
          <div className="relative">
            <FaSearch className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              className="w-full pl-12 pr-4 py-3 text-base border border-gray-300 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-200"
              placeholder="Search by name or license number..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
        </div>

        {/* Table */}
        {loading ? (
          <div className="text-center py-20 text-gray-500">Loading Practitioner...</div>
        ) : (
          <div className="relative rounded-2xl shadow-lg border border-gray-200 bg-white overflow-visible">

            <table className="w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {["Name", "Role", "Specialization", "License Number", "Gender", "Phone", "Email", , "Actions"].map(h => (
                    <th key={h} className="px-4 py-3 text-left text-sm font-semibold text-gray-700 uppercase tracking-wide">{h}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredDoctors.length === 0 ? (
                  <tr><td colSpan={9} className="text-center py-12 text-gray-500">No Practitioner found.</td></tr>
                ) : (
                  filteredDoctors.map((doc) => (
                    <tr key={doc.doctorId} className="hover:bg-blue-50 transition">
                      <td className="px-1 py-4">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold shadow-md">
                            {doc.fullName
                              .split(" ")
                              .map(n => n[0])
                              .slice(0, 2)
                              .join("")
                              .toUpperCase()}
                          </div>
                          <div>
                            <div className="font-medium text-gray-800 whitespace-nowrap overflow-hidden text-ellipsis">
                              {doc.fullName}
                            </div>
                            <div className="text-xs text-gray-500">{doc.roleType} · {doc.specialization}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.roleType}</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.specialization}</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.registrationNumber}</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.gender}</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.mobileNumber}</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.email}</td>
                      <td className="px-4 py-4 text-sm text-gray-700 relative">
                        <div className="flex items-center gap-2">
                          {/* Standard buttons */}
                          <button
                            className="bg-green-100 text-green-600 p-2 rounded-lg hover:bg-green-200"
                            title="View Profile"
                            onClick={() => setSelectedDoctorId(doc.doctorId)}
                          >
                            <FaEye />
                          </button>
                          <button
                            className="bg-blue-100 text-blue-600 p-2 rounded-lg hover:bg-blue-200"
                            title="Edit"
                            onClick={() => navigate(`/doctors/edit/${doc.doctorId}`)}
                          >
                            <FaEdit />
                          </button>
                          <button
                            className="bg-red-100 text-red-600 p-2 rounded-lg hover:bg-red-200"
                            title="Delete"
                            onClick={() => setConfirmDeleteId(doc.doctorId)}
                          >
                            <FaTrash />
                          </button>

                          {/* Dropdown button */}

{doc.roleType === "Doctor" && (
  <div className="relative">
    <button
      id={`dropdown-btn-${doc.doctorId}`}
      onClick={() =>
        setOpenDropdown(openDropdown === doc.doctorId ? null : doc.doctorId)
      }
      className="p-2 rounded-full hover:bg-gray-100 transition"
      title="More actions"
    >
      <FaEllipsisV />
    </button>

    {openDropdown === doc.doctorId &&
  typeof doctorsWithSchedule[doc.doctorId] === "boolean" && (
    <div
      id={`dropdown-menu-${doc.doctorId}`}
      className="absolute top-full mt-2 right-0 w-56 bg-white border border-gray-200 rounded-xl shadow-xl z-50"
    >
      <ul className="py-1 text-sm text-gray-700">
        <li>
          <button
            onClick={() => {
              navigate(`/schedule/form?mode=create&doctorId=${doc.doctorId}`);
              setOpenDropdown(null);
            }}
            className="flex items-center w-full px-4 py-2 hover:bg-gray-100 text-indigo-700"
          >
            <FaCog className="mr-2" />
            Create Schedule
          </button>
        </li>

        {doctorsWithSchedule[doc.doctorId] && (
          <>
            <li>
              <button
                onClick={() => {
                  navigate(`/schedule/view/${doc.doctorId}`);
                  setOpenDropdown(null);
                }}
                className="flex items-center w-full px-4 py-2 hover:bg-gray-100"
              >
                <FaCalendarAlt className="mr-2 text-green-600" />
                View Schedule
              </button>
            </li>
            <li>
              <button
                onClick={() => {
                  navigate(`/schedule/form?mode=edit&doctorId=${doc.doctorId}`);
                  setOpenDropdown(null);
                }}
                className="flex items-center w-full px-4 py-2 hover:bg-gray-100"
              >
                <FaEdit className="mr-2 text-blue-600" />
                Edit Schedule
              </button>
            </li>
           <li>
  <button
    onClick={() => {
        if (doctorsWithSchedule[doc.doctorId]) {
    setConfirmDelete(doc.doctorId);
  } else {
    toast.info("No schedule found for this practitioner.");
  }
  setOpenDropdown(null);
}}
    className="flex items-center w-full px-4 py-2 hover:bg-gray-100 text-red-700"
  >
    <FaTrash className="mr-2" />
    Delete Schedule
  </button>
</li>

          </>
        )}
      </ul>
    </div>
)}

  </div>
)}
  </div>
                      </td>



                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Modals */}
        {selectedDoctorId && (
          <DoctorProfileModal
            doctorId={selectedDoctorId}
            onClose={() => setSelectedDoctorId(null)}
          />
        )}
        <ConfirmDialog
          isOpen={!!confirmDeleteId}
          title="Deactivate Doctor"
          message="Are you sure you want to deactivate this Practitioner? This action cannot be undone."
          onConfirm={handleDelete}
          onCancel={() => setConfirmDeleteId(null)}
          
        />
        <ConfirmDialog
  isOpen={!!confirmDelete}
  title="Delete Schedule"
  message="Are you sure you want to delete this practitioner's schedule?"
  onConfirm={handleConfirmDelete}
  onCancel={() => setConfirmDelete(null)}
/>

        
      </div>
      
    </div>
    
  );
  
};

export default DoctorListPage;