import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { getDoctorById, createDoctor, updateDoctor } from "../../services/doctorApis";
import { ReactSelectInput } from "../../commonfields/ReactSelectInput";
import {allowOnlyLettersWithSpecialChars,allowOnlyNumbersWithLimit,allowOnlyLetters,allowAlphaNumericWithPolicyChars,allowEmailCharactersOnly} from "../../inputhelpers/inputHelpers"

// ADD THIS at the top of the file (below existing imports)

const qualificationMap = {
  "Administrator": [
    "MBA (Hospital Administration)",
    "MHA (Health Administration)",
    "Diploma in Hospital Management", "Others"
  ],
  "Doctor": [
    "MBBS",
    "MD (General Medicine)",
    "MS (General Surgery)",
    "DNB (Diplomate of National Board)",
    "DM (Cardiology)",
    "DM (Neurology)",
    "MCh (Cardiothoracic Surgery)",
    "DO (Doctor of Osteopathy)",
    "BAMS (Ayurveda)",
    "BHMS (Homeopathy)",
    "BUMS (Unani)",
    "MD (Ayurveda)",
    "MD (Homeopathy)",
    "PhD in Medicine",
    "Fellowship in Oncology",
    "Fellowship in Gastroenterology"
  ],
  "FrontOffice": [
    "Diploma in Hospital Management",
    "Certificate in Medical Receptionist Training",
    "BHM (Bachelor in Hospital Management)", "Others"
  ],
  "Report User": [
    "B.Sc (Medical Lab Technology)",
    "DMLT (Diploma in Medical Lab Technology)",
    "B.Sc (Radiology)",
    "Diploma in Radiology"
  ],
  "Therapist": [
    "BPT (Bachelor of Physiotherapy)",
    "MPT (Master of Physiotherapy)",
    "BOT (Bachelor of Occupational Therapy)",
    "MOT (Master of Occupational Therapy)",
    "BASLP (Audiology & Speech Language Pathology)",
    "Diploma in Occupational Therapy"
  ],
  "Patient": [],
  "Pharmacist": [
    "D.Pharm (Diploma in Pharmacy)",
    "B.Pharm (Bachelor of Pharmacy)",
    "M.Pharm (Master of Pharmacy)"
  ],
  "Clinic Manager": [
    "MBA (Healthcare Management)",
    "BHM (Bachelor of Hospital Management)",
    "Diploma in Clinic Administration"
  ],
  "Guest": [],
  "Counsellor": [
    "MA (Psychology)",
    "MSW (Master of Social Work - Medical & Psychiatry)",
    "Diploma in Mental Health Counselling",
    "Others"
  ],
  "Moderator": [
    "B.Sc (Health Information Management)",
    "Certificate in Healthcare IT",
    "Diploma in Medical Records Management"
  ]
};

const ROLE_TYPES = [
  "Administrator", "Doctor", "FrontOffice", "Report User", "Therapist", "Patient",
  "Pharmacist", "Clinic Manager", "Guest", "Counsellor", "Moderator"
];


const SPECIALIZATIONS = [
  "Internal Medicine", "Diabetology", "Infectious Diseases", "Geriatrics",
  "Laparoscopic Surgery", "Breast Surgery", "Trauma Surgery",
  "Joint Replacement", "Spine Surgery", "Sports Medicine",
  "Pediatric Neurology", "Pediatric Cardiology", "Neonatology",
  "Maternal-Fetal Medicine", "Infertility", "Gynecologic Oncology",
  "Interventional Cardiology", "Electrophysiology", "Non-Invasive Cardiology",
  "Stroke", "Epilepsy", "Neurophysiology",
  "Spine Surgery", "Cranial Surgery", "Functional Neurosurgery",
  "Cosmetic Dermatology", "Pediatric Dermatology", "Dermatosurgery",
  "Head & Neck Surgery", "Rhinology", "Otology", "Laryngology",
  "Andrology", "Endourology", "Uro-Oncology",
  "Dialysis", "Transplant Nephrology", "Hypertension",
  "Hepatology", "Therapeutic Endoscopy", "Inflammatory Bowel Disease",
  "Medical Oncology", "Surgical Oncology", "Radiation Oncology",
  "Diagnostic Radiology", "Interventional Radiology", "Neuroradiology",
  "Pain Management", "Critical Care", "Neuroanesthesia",
  "Child Psychiatry", "Addiction Medicine", "Geriatric Psychiatry",
  "Sleep Medicine", "Interventional Pulmonology", "Allergy",
  "Clinical Pathology", "Histopathology", "Cytopathology",
  "Orthodontics", "Endodontics", "Prosthodontics", "Periodontics",
  "Trauma Care", "Toxicology", "Disaster Medicine",
  "Retina", "Cornea", "Glaucoma", "Pediatric Ophthalmology",
  "Physiotherapy", "Occupational Therapy", "Neuro Rehab",
  "Ayurveda (Kayachikitsa, Panchakarma)", "Homeopathy", "Unani"
];

const getFullName = (doctor) => [doctor.firstName, doctor.middleName, doctor.lastName].filter(Boolean).join(" ");

type TextInputProps = {
  label: string;
  name: string;
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  type?: string;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void; // ✅ Add this
};

const TextInput: React.FC<TextInputProps> = ({
  label,
  name,
  value,
  onChange,
  type = "text",
  onKeyDown,
  onBlur,
}) => (
  <div className="space-y-1">
    <label className="block text-sm font-medium text-gray-700">{label}</label>
    <input
      type={type}
      name={name}
      value={value}
      onChange={onChange}
      onKeyDown={onKeyDown}
      onBlur={onBlur}
      required
      className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white"
    />
  </div>
);


const getQualificationOptions = (role) => qualificationMap[role] || [];

const SelectInput = ({ label, name, value, onChange, options }) => (
  <div className="space-y-1">
    <label className="block text-sm font-medium text-gray-700">{label}</label>
    <select
      name={name}
      value={value}
      onChange={onChange}
      required
      className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white"
    >
      <option value="">Select</option>
      {options.map(opt => (
        <option key={opt} value={opt}>{opt}</option>
      ))}
    </select>
  </div>
);

const SearchableSelect = ({ label, name, value, onChange, options }) => {
  const [query, setQuery] = useState("");
  const filtered = options.filter(opt => opt.toLowerCase().includes(query.toLowerCase()));

  return (
    <div className="space-y-1">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Search..."
        className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
      />
      <select
        name={name}
        value={value}
        onChange={(e) => { onChange(e); setQuery(""); }}
        className="w-full mt-1 px-4 py-2 border border-gray-300 rounded-md bg-white"
      >
        <option value="">Select</option>
        {filtered.map(opt => (
          <option key={opt} value={opt}>{opt}</option>
        ))}
      </select>
    </div>
  );
};

const SectionCard = ({ title, color = "blue", children }) => {
  const colorMap = {
    blue: "border-blue-500 bg-blue-50",
    purple: "border-purple-500 bg-purple-50",
    green: "border-green-500 bg-green-50",
    yellow: "border-yellow-500 bg-yellow-50",
  };
  return (
    <div className="rounded-xl border border-gray-200 shadow-sm bg-white">
      <div className={`px-4 py-2 border-l-4 ${colorMap[color]}`}>
        <h2 className="text-base font-semibold text-gray-800">{title}</h2>
      </div>
      <div className="p-4">{children}</div>
    </div>
  );
};

const DoctorFormPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEdit = Boolean(id);

  const [form, setForm] = useState({
    firstName: "",
    middleName: "",
    lastName: "",
    gender: "",
    dateOfBirth: "",
    mobileNumber: "",
    email: "",
    registrationNumber: "",
    registrationState: "",
    yearsOfExperience: 0,
    telemedicineReady: false,
    languagesSpoken: [""],
    isActive: true,
    specialization: "",
    roleType: "",
    qualification: "",
    address: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
      country: "",
    },
  });

  useEffect(() => {
    if (!isEdit || !id) return;
    const loadDoctor = async () => {
      try {
        const response = await getDoctorById(id);
        if (response.success && response.data) {
          const doctor = response.data;
          setForm({
            firstName: doctor.firstName || "",
            middleName: doctor.middleName || "",
            lastName: doctor.lastName || "",
            gender: doctor.gender,
            dateOfBirth: doctor.dateOfBirth,
            mobileNumber: doctor.mobileNumber,
            email: doctor.email,
            registrationNumber: doctor.registrationNumber,
            registrationState: doctor.registrationState || "",
            yearsOfExperience: doctor.yearsOfExperience,
            telemedicineReady: doctor.telemedicineReady,
            languagesSpoken: doctor.languagesSpoken.length > 0 ? doctor.languagesSpoken : [""],
            isActive: doctor.isActive,
            roleType: doctor.roleType,
            specialization: doctor.specialization,
            qualification: doctor.qualification,
            address: { ...doctor.address },
          });
        } else {
          toast.error(`Failed to load doctor: ${response.error}`);
        }
      } catch (error) {
        toast.error("Failed to fetch doctor details. " + (error.message || ""));
      }
    };
    loadDoctor();
  }, [id, isEdit]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (name.startsWith("address.")) {
      const field = name.split(".")[1];
      setForm(prev => ({ ...prev, address: { ...prev.address, [field]: value } }));
    } else {
      setForm(prev => ({ ...prev, [name]: type === "checkbox" ? checked : value }));
    }
  };

  const handleLanguageChange = (index, value) => {
    const updated = [...form.languagesSpoken];
    updated[index] = value;
    setForm(prev => ({ ...prev, languagesSpoken: updated }));
  };

  const addLanguage = () => {
    setForm(prev => ({ ...prev, languagesSpoken: [...prev.languagesSpoken, ""] }));
  };

  const removeLanguage = (index) => {
    const filtered = form.languagesSpoken.filter((_, i) => i !== index);
    setForm(prev => ({ ...prev, languagesSpoken: filtered }));
  };

const handleSubmit = async (e) => {
  e.preventDefault();

  try {
    const isDoctor = form.roleType === "Doctor";

    const payload = {
      ...form,
      specialization: isDoctor ? form.specialization : "N/A",
      registrationNumber: isDoctor ? form.registrationNumber : "N/A",
    };

    const response = isEdit && id
      ? await updateDoctor(id, payload)
      : await createDoctor(payload);

    if (response.success) {
      toast.success(response.message || `Doctor ${isEdit ? "updated" : "created"} successfully.`);
      setTimeout(() => navigate("/doctors"), 500);
    } else {
      // Parse status code from error (if included in the string)
      const [statusCode, errorMsg] = response.error?.split(":", 2) || [];

      if (statusCode === "409") {
        toast.error("A doctor with similar details already exists.");
      } else {
        toast.error(errorMsg || "Something went wrong while saving the doctor.");
      }
    }
  } catch (error) {
    if (error?.response?.status === 409) {
      toast.error("A doctor with similar details already exists.");
    } else {
      toast.error("Unexpected error: " + (error.message || "unknown error"));
    }
  }
};



  // The rest of the JSX rendering remains unchanged
 return (
  <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <ToastContainer />
    <div className="max-w-5xl mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold text-center text-gray-800 mb-8">
        {isEdit ? "Edit Practitioner" : "Register Practitioner"}
      </h1>

      <form onSubmit={handleSubmit} className="space-y-6">
        <SectionCard title="Personal Information" color="blue">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput label="First Name" name="firstName" value={form.firstName} onChange={handleChange} onKeyDown={allowOnlyLettersWithSpecialChars(20)} />
            <TextInput label="Middle Name" name="middleName" value={form.middleName} onChange={handleChange}onKeyDown={allowOnlyLettersWithSpecialChars(20)} />
            <TextInput label="Last Name" name="lastName" value={form.lastName} onChange={handleChange} onKeyDown={allowOnlyLettersWithSpecialChars(20)} />
            <SelectInput label="Gender" name="gender" value={form.gender} onChange={handleChange} options={["Male", "Female", "Other","Unknown"]} />
            <TextInput label="Date of Birth" name="dateOfBirth" type="date" value={form.dateOfBirth} onChange={handleChange} />
            <TextInput label="Mobile Number" name="mobileNumber" value={form.mobileNumber} onChange={handleChange} onKeyDown={allowOnlyNumbersWithLimit(10)} />
            <TextInput label="Email" name="email" type="email" value={form.email} onChange={handleChange} onKeyDown={allowEmailCharactersOnly(30)} />
          </div>
        </SectionCard>
<SectionCard title="Professional Information" color="purple">
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    <SelectInput
      label="Role Type"
      name="roleType"
      value={form.roleType}
      onChange={handleChange}
      options={ROLE_TYPES}
    />

    {form.roleType === "Doctor" && (
      <>
        <ReactSelectInput
          label="Specialization"
          name="specialization"
          value={form.specialization}
          onChange={handleChange}
          options={SPECIALIZATIONS}
        />
        <TextInput
          label="License Number"
          name="registrationNumber"
          value={form.registrationNumber}
          onChange={handleChange}
          onKeyDown={allowAlphaNumericWithPolicyChars()}
        />
        <TextInput
          label="Registration State"
          name="registrationState"
          value={form.registrationState}
          onChange={handleChange}
          onKeyDown={allowOnlyLetters()}
        />
      </>
    )}

    <TextInput
      label="Years of Experience"
      name="yearsOfExperience"
      type="number"
      value={form.yearsOfExperience}
      onChange={handleChange}
      onKeyDown={allowOnlyNumbersWithLimit(2)}
    />

    {form.roleType !== "" && (
      ["Patient", "Guest"].includes(form.roleType) ? (
        <TextInput
          label="Qualification"
          name="qualification"
          value={form.qualification}
          onChange={handleChange}
        />
      ) : (
        <SelectInput
          label="Qualification"
          name="qualification"
          value={form.qualification}
          onChange={handleChange}
          options={getQualificationOptions(form.roleType)}
        />
      )
    )}

    <div className="col-span-2">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        Languages Spoken
      </label>
      <div className="space-y-2">
        {form.languagesSpoken.map((lang: string, index: number) => (
          <div key={index} className="flex items-center gap-2">
            <input
              type="text"
              value={lang}
              onChange={(e) => handleLanguageChange(index, e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
              placeholder={`Language ${index + 1}`}
              onKeyDown={allowOnlyLetters()}
            />
            {form.languagesSpoken.length > 1 && (
              <button
                type="button"
                onClick={() => removeLanguage(index)}
                className="text-red-500 hover:text-red-700"
              >
                ✕
              </button>
            )}
          </div>
        ))}
        <button
          type="button"
          onClick={addLanguage}
          className="text-sm text-blue-600 hover:underline"
        >
          + Add another language
        </button>
      </div>
    </div>
  </div>
</SectionCard>




       <SectionCard title="Address" color="green">
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    <TextInput
      label="Street"
      name="address.street"
      value={form.address.street}
      onChange={handleChange}
      onKeyDown={allowAlphaNumericWithPolicyChars()}
    />
    <TextInput
      label="City"
      name="address.city"
      value={form.address.city}
      onChange={handleChange}
      onKeyDown={allowOnlyLetters()}
    />
    <TextInput
      label="State"
      name="address.state"
      value={form.address.state}
      onChange={handleChange}
      onKeyDown={allowOnlyLetters()}
    />
    <TextInput
      label="Zip Code"
      name="address.zipCode"
      value={form.address.zipCode}
      onChange={handleChange}
      onKeyDown={allowOnlyNumbersWithLimit(6)}
    />
    <TextInput
      label="Country"
      name="address.country"
      value={form.address.country}
      onChange={handleChange}
      onKeyDown={allowOnlyLetters()}
    />
  </div>
</SectionCard>


        <SectionCard title="Status" color="yellow">
          <div className="flex items-center gap-6">
            <label className="flex items-center gap-2 text-sm text-gray-700">
              <input type="checkbox" name="telemedicineReady" checked={form.telemedicineReady} onChange={handleChange} />
              Telemedicine Ready
            </label>
            <label className="flex items-center gap-2 text-sm text-gray-700">
              <input type="checkbox" name="isActive" checked={form.isActive} onChange={handleChange} />
              Active
            </label>
          </div>
        </SectionCard>

        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <button
            type="button"
            onClick={() => navigate("/doctors")}
            className="w-full sm:w-auto px-6 py-2 text-sm font-semibold text-gray-700 bg-white border border-gray-300 rounded-lg shadow hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="w-full sm:w-auto px-6 py-2 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow hover:from-blue-700 hover:to-indigo-700"
          >
            {isEdit ? "Save Changes" : "Register Doctor"}
          </button>
        </div>
      </form>
    </div>
  </div>
);



};

export default DoctorFormPage;
