import React, { useEffect, useState } from "react";
import {
  requestAbhaCardOtp,
  verifyAbhaCardOtp,
  verifyAbhaCardUser,
  fetchAbhaCardImage,
} from "../../services/abhaApis";
import { showError, showSuccess } from "../../utils/toastUtils";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  mobile: string;
};

const GenerateAbhaCardPopup: React.FC<Props> = ({ isOpen, onClose, mobile }) => {
  const [txnId, setTxnId] = useState("");
  const [otp, setOtp] = useState("");
  const [cardImageUrl, setCardImageUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) sendOtp();
  }, [isOpen]);

  const sendOtp = async () => {
    try {
      setLoading(true);
      const res = await requestAbhaCardOtp(mobile);
      setTxnId(res.data.txnId);
      showSuccess("OTP Sent", res.data.message);
    } catch (err: any) {
      showError("Failed", err?.response?.data?.message || "Unknown error");
      onClose();
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyAndGenerate = async () => {
    if (!otp || !txnId) return showError("Error", "Enter OTP");

    try {
      setLoading(true);

      const { data: verifyData } = await verifyAbhaCardOtp(txnId, otp);
      const abhaNumber = verifyData.accounts?.[0]?.ABHANumber;
      const token = verifyData.token;
       if (!abhaNumber) throw new Error("ABHA Number not found in response.");
    if (!token) throw new Error("Token not returned from OTP verification.");

      const { data: verifyUserData } = await verifyAbhaCardUser(abhaNumber, txnId, token);
      const xToken = verifyUserData.token;

      const imageRes = await fetchAbhaCardImage(xToken);
      const blob = new Blob([imageRes.data], { type: "image/png" });
      const url = URL.createObjectURL(blob);
      setCardImageUrl(url);

      showSuccess("ABHA Card Ready", "Card image generated");
    } catch (err: any) {
      showError("Verification Failed", err?.response?.data?.message || "Unexpected error");
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-40 blur-backdrop flex justify-center items-center">
      <div className="bg-white p-6 rounded-lg w-full max-w-md relative shadow-lg">
        <button onClick={onClose} className="absolute top-2 right-3 text-gray-500 text-xl hover:text-black">
          &times;
        </button>

        {!cardImageUrl ? (
          <>
            <h3 className="text-md font-semibold text-gray-800 mb-4">Enter OTP to Generate ABHA Card</h3>
            <input
              type="text"
              value={otp}
              onChange={(e) => setOtp(e.target.value.replace(/\D/g, "").slice(0, 6))}
              placeholder="Enter OTP"
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm mb-4"
              disabled={loading}
            />
            <button
              onClick={handleVerifyAndGenerate}
              className="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700 disabled:opacity-50"
              disabled={loading || otp.length !== 6}
            >
              {loading ? "Processing..." : "Submit & Generate"}
            </button>
          </>
        ) : (
          <div>
            <img src={cardImageUrl} alt="ABHA Card" className="w-full max-w-lg rounded shadow" />
            <a
              href={cardImageUrl}
              download="abha-card.png"
              className="block text-sm mt-3 text-blue-600 underline hover:text-blue-800 text-center"
            >
              Download Card
            </a>
          </div>
        )}
      </div>
    </div>
  );
};

export default GenerateAbhaCardPopup;
