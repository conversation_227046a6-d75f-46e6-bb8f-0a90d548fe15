import React, { useState, useEffect } from 'react';
import { Calendar, Clock, User, CheckCircle, XCircle, Coffee, ChevronLeft, ChevronRight, CalendarDays, CalendarRange } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { showError } from '../../utils/toastUtils';
import { getConsultantSlots, type ConsultantSlot } from '../../services/scheduleApis';
import type { AvailableSlot } from '../../types/schedule';
import { SlotDuration } from '../../types/appointmentenums';

type ViewMode = 'day' | 'week' | 'month';

interface SlotSelectionPanelProps {
  providerId: string;
  selectedDate: string;
  onSlotSelect: (slot: AvailableSlot) => void;
  selectedSlot?: AvailableSlot | null;
  className?: string;
}

export const SlotSelectionPanel: React.FC<SlotSelectionPanelProps> = ({
  providerId,
  selectedDate,
  onSlotSelect,
  selectedSlot,
  className = ''
}) => {
  const [slots, setSlots] = useState<AvailableSlot[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDateSlots, setSelectedDateSlots] = useState<AvailableSlot[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('day');
  const [currentDate, setCurrentDate] = useState(new Date(selectedDate || new Date()));
  const [weekSlots, setWeekSlots] = useState<AvailableSlot[]>([]);
  const [monthSlots, setMonthSlots] = useState<AvailableSlot[]>([]);

  useEffect(() => {
    if (selectedDate) {
      setCurrentDate(new Date(selectedDate));
    }
  }, [selectedDate]);

  useEffect(() => {
    if (providerId) {
      loadSlotsAndAppointments();
    } else {
      // Clear all slots when no provider is selected
      setSlots([]);
      setSelectedDateSlots([]);
      setWeekSlots([]);
      setMonthSlots([]);
    }
  }, [providerId, currentDate, viewMode]);

  // Clear slots immediately when provider changes to prevent showing stale data
  useEffect(() => {
    setSlots([]);
    setSelectedDateSlots([]);
    setWeekSlots([]);
    setMonthSlots([]);
  }, [providerId]);

  const getDateRange = () => {
    const currentDateStr = currentDate.toISOString().split('T')[0];

    switch (viewMode) {
      case 'day':
        return { fromDate: currentDateStr, toDate: currentDateStr };

      case 'week':
        const startOfWeek = new Date(currentDate);
        startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return {
          fromDate: startOfWeek.toISOString().split('T')[0],
          toDate: endOfWeek.toISOString().split('T')[0]
        };

      case 'month':
        const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
        return {
          fromDate: startOfMonth.toISOString().split('T')[0],
          toDate: endOfMonth.toISOString().split('T')[0]
        };

      default:
        return { fromDate: currentDateStr, toDate: currentDateStr };
    }
  };

  // Transform ConsultantSlot[] to AvailableSlot[]
  const transformConsultantSlotsToAvailableSlots = (consultantSlots: ConsultantSlot[]): AvailableSlot[] => {
    console.log('🔄 Transforming consultant slots:', consultantSlots);

    const transformed = consultantSlots.map((slot, index) => {
      console.log(`🔍 Processing slot ${index + 1}:`, slot);

      const availableSlot = {
        slotId: `${slot.consultantId}-${slot.slotDate}-${slot.slotNumber}`,
        providerId: slot.consultantId,
        date: slot.slotDate,
        startTime: slot.startTime.substring(0, 8), // Convert HH:MM:SS.nnnnnnnnn to HH:MM:SS
        endTime: slot.endTime.substring(0, 8),
        duration: calculateSlotDuration(slot.startTime, slot.endTime),
        slotNumber: slot.slotNumber,
        isAvailable: slot.availability === 'OPEN',
        isBooked: slot.availability === 'BOOKED',
        isBlocked: slot.availability === 'BLOCKED',
        appointmentId: slot.availability === 'BOOKED' ? `appointment-${slot.slotNumber}` : undefined,
        blockReason: slot.availability === 'BLOCKED' ? 'Blocked by provider' : undefined
      };

      console.log(`✅ Transformed slot ${index + 1}:`, availableSlot);
      return availableSlot;
    });

    console.log('🎯 Final transformed slots:', transformed);
    return transformed;
  };

  // Calculate slot duration in minutes and return appropriate SlotDuration enum
  const calculateSlotDuration = (startTime: string, endTime: string): SlotDuration => {
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    const durationMinutes = Math.round((end.getTime() - start.getTime()) / (1000 * 60));

    // Map duration to SlotDuration enum
    switch (durationMinutes) {
      case 15: return SlotDuration.Fifteen;
      case 30: return SlotDuration.Thirty;
      case 45: return SlotDuration.FortyFive;
      case 60: return SlotDuration.Sixty;
      case 90: return SlotDuration.Ninety;
      case 120: return SlotDuration.TwoHours;
      default: return SlotDuration.Thirty; // Default to 30 minutes
    }
  };

  // Load slots for multiple dates (week/month view)
  const loadSlotsForDateRange = async (fromDate: string, toDate: string) => {
    const allSlots: AvailableSlot[] = [];
    const startDate = new Date(fromDate);
    const endDate = new Date(toDate);

    // Generate array of dates between fromDate and toDate
    const dates: string[] = [];
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      dates.push(d.toISOString().split('T')[0]);
    }

    // Load slots for each date
    for (const date of dates) {
      try {
        const response = await getConsultantSlots(providerId, date);
        if (response.success && response.data) {
          const transformedSlots = transformConsultantSlotsToAvailableSlots(response.data);
          allSlots.push(...transformedSlots);
        }
      } catch (error) {
        console.error(`Failed to load slots for date ${date}:`, error);
      }
    }

    console.log('📊 Loaded slots for date range:', {
      slotsCount: allSlots.length,
      dateRange: `${fromDate} to ${toDate}`,
      viewMode
    });

    setSlots(allSlots);

    // Filter slots based on view mode
    if (viewMode === 'week') {
      setWeekSlots(allSlots);
    } else if (viewMode === 'month') {
      setMonthSlots(allSlots);
    }
  };

  const loadSlotsAndAppointments = async () => {
    setLoading(true);
    try {
      const { fromDate, toDate } = getDateRange();

      console.log('🔄 Loading slots and appointments for:', { providerId, fromDate, toDate, viewMode });

      // For day view, use specific date; for week/month, we'll need to make multiple calls
      if (viewMode === 'day') {
        console.log('🔍 Fetching slots for:', { providerId, date: fromDate });
        const response = await getConsultantSlots(providerId, fromDate);

        console.log('📡 API Response:', {
          success: response.success,
          dataLength: response.data?.length || 0,
          error: response.error,
          rawData: response.data
        });

        if (response.success && response.data) {
          if (response.data.length === 0) {
            console.warn('⚠️ No slots found for this date. Slots may need to be generated first.');
            showError(`No slots available for ${fromDate}. Please generate slots first or select a different date.`);
            setSlots([]);
            setSelectedDateSlots([]);
            setWeekSlots([]);
            setMonthSlots([]);
            return;
          }

          const transformedSlots = transformConsultantSlotsToAvailableSlots(response.data);
          console.log('✅ Transformed slots:', {
            originalCount: response.data.length,
            transformedCount: transformedSlots.length,
            sampleSlot: transformedSlots[0]
          });

          setSlots(transformedSlots);

          // Filter slots for day view immediately after setting them
          const currentDateStr = currentDate.toISOString().split('T')[0];
          const dateSlots = transformedSlots.filter(slot => slot.date === currentDateStr);
          console.log('🎯 Filtered slots for day view:', {
            currentDate: currentDateStr,
            totalSlots: transformedSlots.length,
            filteredSlots: dateSlots.length,
            dateSlots
          });
          setSelectedDateSlots(dateSlots);
        } else {
          console.error('❌ Failed to load consultant slots:', response.error);
          showError(response.error || 'Failed to load available slots');
          setSlots([]);
          setSelectedDateSlots([]);
          setWeekSlots([]);
          setMonthSlots([]);
          return;
        }
      } else {
        // For week/month view, we need to load slots for multiple dates
        await loadSlotsForDateRange(fromDate, toDate);
        return;
      }

      // Filtering is now handled directly in the day view block above

    } catch (error) {
      console.error('Failed to load slots and appointments:', error);
      showError('Failed to load available slots');
      // Clear all slot states on error
      setSlots([]);
      setSelectedDateSlots([]);
      setWeekSlots([]);
      setMonthSlots([]);
    } finally {
      setLoading(false);
    }
  };

  const getSlotStatus = (slot: AvailableSlot): 'available' | 'booked' | 'blocked' | 'break' => {
    if (slot.isBlocked) return 'blocked';
    if (slot.blockReason?.toLowerCase().includes('break') ||
        slot.blockReason?.toLowerCase().includes('lunch')) return 'break';
    if (slot.isBooked) return 'booked';
    return 'available';
  };

  const getSlotStatusColor = (slot: AvailableSlot): string => {
    const status = getSlotStatus(slot);
    switch (status) {
      case 'available':
        return 'bg-green-100 border-green-300 text-green-800 hover:bg-green-200 cursor-pointer';
      case 'booked':
        return 'bg-red-100 border-red-300 text-red-800 cursor-not-allowed';
      case 'blocked':
        return 'bg-gray-100 border-gray-300 text-gray-600 cursor-not-allowed';
      case 'break':
        return 'bg-yellow-100 border-yellow-300 text-yellow-800 cursor-not-allowed';
      default:
        return 'bg-gray-100 border-gray-300 text-gray-600';
    }
  };

  const getSlotStatusIcon = (slot: AvailableSlot) => {
    const status = getSlotStatus(slot);
    switch (status) {
      case 'available':
        return <CheckCircle size={16} className="text-green-600" />;
      case 'booked':
        return <XCircle size={16} className="text-red-600" />;
      case 'blocked':
        return <XCircle size={16} className="text-gray-600" />;
      case 'break':
        return <Coffee size={16} className="text-yellow-600" />;
      default:
        return <Clock size={16} className="text-gray-600" />;
    }
  };

  const getSlotStatusText = (slot: AvailableSlot): string => {
    const status = getSlotStatus(slot);
    switch (status) {
      case 'available':
        return 'Available';
      case 'booked':
        return 'Booked';
      case 'blocked':
        return slot.blockReason || 'Blocked';
      case 'break':
        return 'Break Time';
      default:
        return 'Unknown';
    }
  };

  const handleSlotClick = (slot: AvailableSlot) => {
    const status = getSlotStatus(slot);
    if (status === 'available') {
      onSlotSelect(slot);
    }
  };

  const formatTime = (timeString: string): string => {
    // Handle both HH:MM:SS and HH:MM formats
    return timeString.substring(0, 5);
  };

  const formatDateHeader = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getViewTitle = (): string => {
    const currentDateStr = currentDate.toISOString().split('T')[0];

    switch (viewMode) {
      case 'day':
        return formatDateHeader(currentDateStr);

      case 'week':
        const startOfWeek = new Date(currentDate);
        startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return `Week of ${startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;

      case 'month':
        return currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

      default:
        return formatDateHeader(currentDateStr);
    }
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);

    switch (viewMode) {
      case 'day':
        newDate.setDate(currentDate.getDate() + (direction === 'next' ? 1 : -1));
        break;
      case 'week':
        newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case 'month':
        newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
    }

    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const getWeekDays = (): Date[] => {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());

    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      days.push(day);
    }
    return days;
  };

  const getMonthDays = (): Date[] => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    const days = [];
    for (let i = 1; i <= lastDay.getDate(); i++) {
      days.push(new Date(year, month, i));
    }
    return days;
  };

  const getSlotsForDate = (date: Date): AvailableSlot[] => {
    const dateStr = date.toISOString().split('T')[0];
    return slots.filter(slot => slot.date === dateStr);
  };

  const getSlotsSummaryForDate = (date: Date) => {
    const dateSlots = getSlotsForDate(date);
    return {
      total: dateSlots.length,
      available: dateSlots.filter(s => getSlotStatus(s) === 'available').length,
      booked: dateSlots.filter(s => getSlotStatus(s) === 'booked').length,
      unavailable: dateSlots.filter(s => ['blocked', 'break'].includes(getSlotStatus(s))).length
    };
  };

  if (!providerId) {
    return (
      <div className={`bg-gray-50 rounded-lg p-6 text-center ${className}`}>
        <User className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-600 mb-2">Select a Provider</h3>
        <p className="text-gray-500">Choose a provider to view available appointment slots.</p>
      </div>
    );
  }

  if (!selectedDate) {
    return (
      <div className={`bg-gray-50 rounded-lg p-6 text-center ${className}`}>
        <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-600 mb-2">Select a Date</h3>
        <p className="text-gray-500">Choose a date to view available appointment slots.</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-indigo-600" />
            <h3 className="text-lg font-semibold text-gray-800">Available Slots</h3>
          </div>

          {/* View Mode Selector */}
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
            <Button
              type="button"
              onClick={() => setViewMode('day')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                viewMode === 'day'
                  ? 'bg-white text-indigo-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <Calendar className="h-4 w-4 mr-1" />
              Day
            </Button>
            <Button
              type="button"
              onClick={() => setViewMode('week')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                viewMode === 'week'
                  ? 'bg-white text-indigo-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <CalendarDays className="h-4 w-4 mr-1" />
              Week
            </Button>
            <Button
              type="button"
              onClick={() => setViewMode('month')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                viewMode === 'month'
                  ? 'bg-white text-indigo-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <CalendarRange className="h-4 w-4 mr-1" />
              Month
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              onClick={() => navigateDate('prev')}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              type="button"
              onClick={() => navigateDate('next')}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <h4 className="text-base font-medium text-gray-800 ml-2">
              {getViewTitle()}
            </h4>
          </div>

          <Button
            type="button"
            onClick={goToToday}
            className="px-3 py-1 text-sm text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded-md"
          >
            Today
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading available slots...</p>
          </div>
        ) : (
          <>
            {/* Day View */}
            {viewMode === 'day' && (
              <>
                {console.log('🖥️ Rendering day view with selectedDateSlots:', {
                  length: selectedDateSlots.length,
                  slots: selectedDateSlots,
                  currentDate: currentDate.toISOString().split('T')[0]
                })}
                {selectedDateSlots.length === 0 ? (
                  <div className="text-center py-8">
                    <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-600 mb-2">No Slots Available</h3>
                    <p className="text-gray-500 mb-4">
                      No appointment slots are available for the selected date and provider.
                    </p>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
                      <h4 className="text-sm font-medium text-blue-800 mb-2">💡 To create slots:</h4>
                      <ol className="text-sm text-blue-700 space-y-1">
                        <li>1. Go to <strong>Schedule Management</strong> page</li>
                        <li>2. Create a schedule configuration for this doctor</li>
                        <li>3. Generate slots for the desired date range</li>
                        <li>4. Return here to book appointments</li>
                      </ol>
                    </div>
                  </div>
                ) : (
          <>
            {/* Legend */}
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Legend:</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center space-x-2">
                  <CheckCircle size={14} className="text-green-600" />
                  <span className="text-gray-600">Available</span>
                </div>
                <div className="flex items-center space-x-2">
                  <XCircle size={14} className="text-red-600" />
                  <span className="text-gray-600">Booked</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Coffee size={14} className="text-yellow-600" />
                  <span className="text-gray-600">Break Time</span>
                </div>
                <div className="flex items-center space-x-2">
                  <XCircle size={14} className="text-gray-600" />
                  <span className="text-gray-600">Blocked</span>
                </div>
              </div>
            </div>

            {/* Slots Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-96 overflow-y-auto">
              {selectedDateSlots.map((slot, index) => {
                const isSelected = selectedSlot?.slotId === slot.slotId ||
                  (selectedSlot?.startTime === slot.startTime && selectedSlot?.date === slot.date);
                
                return (
                  <div
                    key={slot.slotId || index}
                    onClick={() => handleSlotClick(slot)}
                    className={`
                      p-3 rounded-lg border-2 transition-all duration-200
                      ${getSlotStatusColor(slot)}
                      ${isSelected ? 'ring-2 ring-indigo-500 ring-offset-2' : ''}
                    `}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <div className="text-sm font-medium">
                        {formatTime(slot.startTime)} - {formatTime(slot.endTime)}
                      </div>
                      {getSlotStatusIcon(slot)}
                    </div>
                    <div className="text-xs">
                      {getSlotStatusText(slot)}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {slot.duration} min
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Selected Slot Info */}
            {selectedSlot && (
              <div className="mt-4 p-3 bg-indigo-50 border border-indigo-200 rounded-lg">
                <h4 className="text-sm font-medium text-indigo-800 mb-1">Selected Slot:</h4>
                <div className="text-sm text-indigo-700">
                  {formatTime(selectedSlot.startTime)} - {formatTime(selectedSlot.endTime)} 
                  ({selectedSlot.duration} minutes)
                </div>
              </div>
            )}

                    {/* Summary */}
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <div className="text-lg font-semibold text-green-600">
                            {selectedDateSlots.filter(s => getSlotStatus(s) === 'available').length}
                          </div>
                          <div className="text-xs text-gray-600">Available</div>
                        </div>
                        <div>
                          <div className="text-lg font-semibold text-red-600">
                            {selectedDateSlots.filter(s => getSlotStatus(s) === 'booked').length}
                          </div>
                          <div className="text-xs text-gray-600">Booked</div>
                        </div>
                        <div>
                          <div className="text-lg font-semibold text-gray-600">
                            {selectedDateSlots.filter(s => ['blocked', 'break'].includes(getSlotStatus(s))).length}
                          </div>
                          <div className="text-xs text-gray-600">Unavailable</div>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </>
            )}

            {/* Week View */}
            {viewMode === 'week' && (
              <div className="space-y-4">
                {getWeekDays().map((day, index) => {
                  const daySlots = getSlotsForDate(day);
                  const summary = getSlotsSummaryForDate(day);
                  const isToday = day.toDateString() === new Date().toDateString();
                  const dayStr = day.toISOString().split('T')[0];

                  return (
                    <div key={index} className={`border rounded-lg p-4 ${isToday ? 'border-indigo-300 bg-indigo-50' : 'border-gray-200'}`}>
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <h4 className={`font-medium ${isToday ? 'text-indigo-800' : 'text-gray-800'}`}>
                            {day.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })}
                          </h4>
                          {isToday && (
                            <span className="px-2 py-1 text-xs bg-indigo-600 text-white rounded-full">Today</span>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm">
                          <span className="text-green-600 font-medium">{summary.available} Available</span>
                          <span className="text-red-600">{summary.booked} Booked</span>
                          <span className="text-gray-600">{summary.unavailable} Unavailable</span>
                        </div>
                      </div>

                      {daySlots.length === 0 ? (
                        <p className="text-gray-500 text-sm">No slots available</p>
                      ) : (
                        <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
                          {daySlots.slice(0, 12).map((slot, slotIndex) => {
                            const isSelected = selectedSlot?.slotId === slot.slotId ||
                              (selectedSlot?.startTime === slot.startTime && selectedSlot?.date === slot.date);

                            return (
                              <div
                                key={slotIndex}
                                onClick={() => handleSlotClick(slot)}
                                className={`
                                  p-2 rounded text-xs text-center transition-all duration-200
                                  ${getSlotStatusColor(slot)}
                                  ${isSelected ? 'ring-2 ring-indigo-500 ring-offset-1' : ''}
                                `}
                              >
                                <div className="font-medium">
                                  {formatTime(slot.startTime)}
                                </div>
                                <div className="flex items-center justify-center mt-1">
                                  {getSlotStatusIcon(slot)}
                                </div>
                              </div>
                            );
                          })}
                          {daySlots.length > 12 && (
                            <div className="p-2 text-xs text-gray-500 text-center">
                              +{daySlots.length - 12} more
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}

            {/* Month View */}
            {viewMode === 'month' && (
              <div className="space-y-4">
                <div className="grid grid-cols-7 gap-2 mb-4">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                    <div key={day} className="text-center text-sm font-medium text-gray-600 py-2">
                      {day}
                    </div>
                  ))}
                </div>

                <div className="grid grid-cols-7 gap-2">
                  {getMonthDays().map((day, index) => {
                    const summary = getSlotsSummaryForDate(day);
                    const isToday = day.toDateString() === new Date().toDateString();
                    const hasSlots = summary.total > 0;

                    return (
                      <div
                        key={index}
                        onClick={() => {
                          setCurrentDate(day);
                          setViewMode('day');
                        }}
                        className={`
                          p-3 border rounded-lg cursor-pointer transition-all duration-200 hover:border-indigo-300
                          ${isToday ? 'border-indigo-300 bg-indigo-50' : 'border-gray-200'}
                          ${hasSlots ? 'hover:bg-gray-50' : 'opacity-60'}
                        `}
                      >
                        <div className={`text-sm font-medium mb-1 ${isToday ? 'text-indigo-800' : 'text-gray-800'}`}>
                          {day.getDate()}
                        </div>

                        {hasSlots ? (
                          <div className="space-y-1">
                            <div className="flex items-center justify-between text-xs">
                              <span className="text-green-600">{summary.available}</span>
                              <span className="text-red-600">{summary.booked}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-1">
                              <div
                                className="bg-green-500 h-1 rounded-full"
                                style={{
                                  width: `${summary.total > 0 ? (summary.available / summary.total) * 100 : 0}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        ) : (
                          <div className="text-xs text-gray-400">No slots</div>
                        )}
                      </div>
                    );
                  })}
                </div>

                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Legend:</h4>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-500 rounded"></div>
                      <span className="text-gray-600">Available slots</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded"></div>
                      <span className="text-gray-600">Booked slots</span>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">Click on any date to view detailed slots</p>
                </div>
              </div>
            )}

            {/* View Summary */}
            <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-4">
                  <span className="text-gray-600">
                    <strong>View:</strong> {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}
                  </span>
                  <span className="text-gray-600">
                    <strong>Period:</strong> {getViewTitle()}
                  </span>
                </div>
                <div className="flex items-center space-x-4 text-xs">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Available</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span>Booked</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                    <span>Unavailable</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Selected Slot Info - Show for all views */}
            {selectedSlot && (
              <div className="mt-4 p-3 bg-indigo-50 border border-indigo-200 rounded-lg">
                <h4 className="text-sm font-medium text-indigo-800 mb-1">✅ Selected Slot:</h4>
                <div className="text-sm text-indigo-700">
                  <strong>{formatTime(selectedSlot.startTime)} - {formatTime(selectedSlot.endTime)}</strong>
                  ({selectedSlot.duration} minutes) on <strong>{formatDateHeader(selectedSlot.date)}</strong>
                </div>
                <div className="text-xs text-indigo-600 mt-1">
                  Click "Create Appointment" to book this slot
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
