import React from 'react';
import { WaitingRoomDisplay } from '../components/waitingRoom/WaitingRoomDisplay';

interface NewWaitingRoomPageProps {
  facilityId?: string;
  displayMode?: 'full' | 'compact';
}

const NewWaitingRoomPage: React.FC<NewWaitingRoomPageProps> = ({ 
  facilityId = "fac-001", 
  displayMode = 'full' 
}) => {
  return (
    <WaitingRoomDisplay 
      facilityId={facilityId}
      autoRefresh={true}
      refreshInterval={30}
    />
  );
};

export default NewWaitingRoomPage;
