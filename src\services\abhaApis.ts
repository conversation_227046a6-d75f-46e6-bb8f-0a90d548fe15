import axios from 'axios';

const ABHA_BASE_URL = import.meta.env.VITE_ABHA_BASE_URL;
// const BASE_URL = "https://megha-dev.sirobilt.com";


interface AadhaarOtpPayload {
  aadhaarNumber: string;
}

interface EnrollByOtpPayload {
  txnId: string;
  otp: string;
  mobile: string;
}

interface VerifyAbhaPayload {
  abhaNumber: string;
}

export const requestAadhaarOtp = async (payload: AadhaarOtpPayload) => {
  try {
    const response = await axios.post(`${ABHA_BASE_URL}/abha/aadhaar-otp`, payload);
    return { success: true, data: response.data };
  } catch (error: any) {
    console.error("OTP Request Error:", error.response?.data || error.message);
    return { success: false, error: error.response?.data || error.message };
  }
};

export const enrollAbhaByOtp = async (payload: EnrollByOtpPayload) => {
  try {
    const response = await axios.post(`${ABHA_BASE_URL}/abha/enroll/by-otp`, payload);
    return { success: true, data: response.data };
  } catch (error: any) {
    console.error("Enroll by OTP Error:", error.response?.data || error.message);
    return { success: false, error: error.response?.data || error.message };
  }
};

export const verifyAbhaNumber = async (payload: VerifyAbhaPayload) => {

  try {
    const response = await axios.post(`${ABHA_BASE_URL}/abha/verify`, payload);
     return { success: true, data: response.data };
  } catch (error: any) {
    console.error("ABHA Verification Error:", error.response?.data || error.message);
    return { success: false, error: error.response?.data || error.message };
  }
};
export const requestOtp = async (payload: { mobile?: string; abhaNumber?: string }) => {
  const res = await axios.post(`${ABHA_BASE_URL}/abha/mobile-login/request-otp`, payload);
  return res.data;
};
 
export const verifyOtp = async (payload: { txnId: string; otp: string; type: "mobile" | "abha" }) => {
  try {
    const res = await axios.post(`${ABHA_BASE_URL}/abha/mobile-login/verify-otp`, payload);
    return { success: true, data: res.data };
  } catch (error: any) {
    console.error("verifyOtp error:", error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data || { message: "Failed to verify OTP" },
    };
  }
};
 
export const verifyUser = async (
  payload: { txnId: string; abhaNumber: string },
  token: string
) => {
  try {
    const res = await axios.post(
      `${ABHA_BASE_URL}/abha/mobile-login/verify-user`,
      payload,
      {
        headers: {
          "T-token": token,
        },
      }
    );
    return res.data;
  } catch (error: any) {
    console.error("verifyUser error:", error.response?.data || error.message);
    throw new Error(error.response?.data?.message || "Failed to verify user");
  }
};


//Abha card 

export const requestAbhaCardOtp = async (mobile: string) => {
  return await axios.post(`${ABHA_BASE_URL}/abha-card/request-otp`, { mobile });
};

export const verifyAbhaCardOtp = async (txnId: string, otp: string) => {
  return await axios.post(`${ABHA_BASE_URL}/abha-card/verify-otp`, {
    type: "mobile",
    txnId,
    otp,
  });
};

export const verifyAbhaCardUser = async (abhaNumber: string, txnId: string, token: string) => {
  return await axios.post(
    `${ABHA_BASE_URL}/abha-card/verify-user`,
    { abhaNumber, txnId },
    { headers: { "T-token": token } }
  );
};

export const fetchAbhaCardImage = async (xToken: string) => {
  return await axios.get(`${ABHA_BASE_URL}/abha-card/generate`, {
    params: { xToken },
    responseType: "blob",
  });
};