import { Routes, Route, Navigate, Outlet, useLocation } from "react-router-dom";
import Homepage from "../pages/Homepage";
import { Layout } from '../components/layouts/Layout';
import PatientPage from "../pages/PatientPage";
import PatientListPage from "../pages/PatientListPage";
import AbhaRegistrationForm from "../components/Abha/Aadhaar/AbhaRegistrationForm";
import AppointmentsPage from "../pages/AppointmentsPage";
import { QueueManagementPage } from "../pages/QueueManagementPage";
import ScheduleManagementPage from "../pages/ScheduleManagementPage";
import Login from "../authentication/Login";
import SignupPage from "../authentication/SignupPage";
import DoctorInterfaceWrapper from "../pages/DoctorInterfaceWrapper";
import WaitingRoomDisplay from "../pages/WaitingRoomDisplay";
import MinimalistWaitingRoomPage from "../pages/MinimalistWaitingRoomPage";
import WaitingRoomDemo from "../pages/WaitingRoomDemo";
import { DepartmentManagement } from "../components/departments/DepartmentManagement";
import { ProviderDepartmentMappingComponent } from "../components/departments/ProviderDepartmentMapping";
import { NewWaitingRoomPage } from "../pages/NewWaitingRoomPage";
import DoctorListPage from "../components/doctor/DoctorsListPage";

import PatientDetails from "../components/patients/sections/PatientDetails"
import QrScanner from "../utils/QrScanner";
import EditDoctorPage from "../components/doctor/DoctorFormPage";
import ViewDoctorSchedule from "../components/doctor/ViewDoctorSchedule";
import ManageAvailabilityPage from "../components/doctor/ManageAvailabilityPage";
import DoctorFormPage from "../components/doctor/DoctorFormPage";

// Route guard for protected routes
const PrivateRoute = () => {
  const isAuthenticated = localStorage.getItem("loggedInUser");
  const location = useLocation();

  return isAuthenticated ? (
    <Outlet />
  ) : (
    <Navigate to="/login" replace state={{ from: location }} />
  );
};


export const AppRoutes = () => (
  <Routes>
    {/* Public login route */}
    <Route path="/login" element={<Login />} />
    <Route path="/signup" element={<SignupPage />} />
   

    {/* Protected application routes */}
    <Route element={<PrivateRoute />}>
      <Route path="/" element={<Layout />}>
        <Route index element={<Homepage />} />
         <Route path="/scan" element={<QrScanner />} />
        <Route path="patients" element={<PatientPage />} />
        <Route path="patients/:id" element={<PatientPage />} />
         <Route path="patient/:patientId" element={<PatientDetails />} />
        <Route path="list" element={<PatientListPage />} />
        <Route path="abha-aadhaar" element={<AbhaRegistrationForm />} />
        <Route path="appointments" element={<AppointmentsPage />} />
        <Route path="schedules" element={<ScheduleManagementPage />} />
        <Route path="departments" element={<DepartmentManagement />} />
        <Route path="provider-mapping" element={<ProviderDepartmentMappingComponent />} />
        <Route path="waiting-room" element={<NewWaitingRoomPage />} />
        <Route path="doctors" element={<DoctorListPage></DoctorListPage>}></Route>
          <Route path="/doctors/edit/:id" element={<EditDoctorPage/>} />
          <Route path="/doctors/add" element={<DoctorFormPage />} />
        <Route path="/doctors/schedule/:id" element={<ViewDoctorSchedule/>} />
        <Route path="/doctors/availability/:id" element={<ManageAvailabilityPage />} />

      <Route path="queue" element={<QueueManagementPage />} />
      <Route path="doctor-interface" element={<DoctorInterfaceWrapper />} />
      </Route>
    </Route>
     {/* <Route path="waiting-room" element={<WaitingRoomDisplay facilityId="1" />} /> */}
    <Route path="waiting-room-compact" element={<WaitingRoomDisplay facilityId="1" displayMode="compact" />} />
    <Route path="waiting-room-department" element={<WaitingRoomDisplay facilityId="1" displayMode="department" />} />
    <Route path="waiting-room-minimalist" element={<MinimalistWaitingRoomPage />} />
    <Route path="waiting-room-simple" element={<WaitingRoomDisplay facilityId="1" displayMode="minimalist" />} />
  </Routes>
);
