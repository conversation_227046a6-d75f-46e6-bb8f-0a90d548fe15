import React, { useEffect, useRef, useState } from "react";
import { Html5Qrcode } from "html5-qrcode";
import { usePatientFormStore } from "../../store/patientFormStore";
import { mapAbhaProfileToPatient } from "../../utils/mapAbhaToPatient";
import { showSuccess, showError } from "../../utils/toastUtils";

const qrRegionId = "abha-qr-region";

type AbhaQRScannerProps = {
  onClose: () => void;
};

const AbhaQRScanner: React.FC<AbhaQRScannerProps> = ({ onClose }) => {
  const scannerRef = useRef<Html5Qrcode | null>(null);
  const lastToastTimeRef = useRef<number>(0);
  const timeoutRef = useRef<number | null>(null);

  const [scannerError, setScannerError] = useState<string | null>(null);
  const [scannedData, setScannedData] = useState<any>(null);

  useEffect(() => {
    let destroyed = false;

    const waitForRegion = async (): Promise<HTMLElement> => {
      const region = document.getElementById(qrRegionId);
      if (region) return region;
      await new Promise((resolve) => setTimeout(resolve, 100));
      return waitForRegion();
    };

    const startScanner = async () => {
      await waitForRegion();
      if (destroyed) return;

      const scanner = new Html5Qrcode(qrRegionId);
      scannerRef.current = scanner;

      try {
        const cameras = await Html5Qrcode.getCameras();
        if (!cameras.length) {
          setScannerError("No camera devices found.");
          return;
        }

        const cameraId = cameras[0].id;

        await scanner.start(
          cameraId,
          { fps: 10, qrbox: { width: 350, height: 350 } },
          async (decodedText) => {
            try {
              const parsed = JSON.parse(decodedText);

              // Keep raw scanned data for button click mapping
              setScannedData(parsed);

              showSuccess("✅ ABHA QR Scanned", "Click below to populate patient data.");

              await scanner.stop();
              await scanner.clear();
              scannerRef.current = null;

              if (timeoutRef.current) clearTimeout(timeoutRef.current);
            } catch (err) {
              const now = Date.now();
              if (now - lastToastTimeRef.current > 20000) {
                lastToastTimeRef.current = now;
                showError("Invalid QR Code", "Unable to parse ABHA QR.");
              }
            }
          },
          (scanError) => {
            console.warn("Scan error:", scanError);
          }
        );

        timeoutRef.current = window.setTimeout(() => {
          if (scannerRef.current && !scannedData) {
            showError("Invalid QR Code", "No valid QR code detected in 20 seconds.");
          }
        }, 20000);
      } catch (err) {
        console.error("Camera init error:", err);
        setScannerError("Camera access failed. Please allow permissions or try another browser.");
      }
    };

    startScanner();

    return () => {
      destroyed = true;
      if (scannerRef.current) {
        scannerRef.current
          .stop()
          .then(() => scannerRef.current?.clear())
          .catch((err) => console.warn("Teardown failed:", err))
          .finally(() => {
            scannerRef.current = null;
          });
      }
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [onClose]);

  const handlePopulate = () => {
    if (!scannedData) return;

    const [firstName, ...rest] = scannedData.name?.split(" ") || [];
    const lastName = rest.pop();
    const middleName = rest.join(" ") || null;

    const abhaFormatted = {
      ABHANumber: scannedData.hidn,
      preferredAbhaAddress: scannedData.hid,
      firstName: firstName || null,
      middleName,
      lastName: lastName || null,
      gender: scannedData.gender,
      dob: scannedData.dob,
      mobile: scannedData.mobile,
      address: scannedData.address,
      stateName: scannedData["state name"],
      districtName: scannedData.district_name,
      email: scannedData.email || null,
      pinCode: scannedData.pincode || scannedData.pinCode || null,
      phrAddress: scannedData.phrAddress,
    };

    try {
      const mapped = mapAbhaProfileToPatient(abhaFormatted);
      usePatientFormStore.getState().setQuickFormData(mapped);
      showSuccess("✅ Patient Data Populated", "Data from QR successfully mapped.");
      onClose();
    } catch (err) {
      showError("Population Failed", "Scanned data is not valid.");
    }
  };

  const handleClose = async () => {
    try {
      if (scannerRef.current) {
        await scannerRef.current.stop();
        await scannerRef.current.clear();
        scannerRef.current = null;
      }
    } catch (err) {
      console.warn("Error stopping scanner:", err);
    } finally {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      onClose();
    }
  };

  return (
    <div className="flex flex-col items-center justify-center gap-3">
      {scannerError ? (
        <div className="text-red-600 text-sm p-4 border border-red-200 rounded-md bg-red-50 max-w-xs">
          {scannerError}
        </div>
      ) : (
        <div className="relative w-[350px] aspect-square rounded-md overflow-hidden border border-gray-300 shadow">
          <div id={qrRegionId} className="w-full h-full" />
          <button
            onClick={handleClose}
            className="absolute top-2 right-2 bg-white border border-gray-300 text-gray-600 rounded-full w-7 h-7 text-sm font-bold shadow hover:bg-gray-100"
            title="Close Scanner"
          >
            ×
          </button>
        </div>
      )}

      {scannedData && (
        <button
          onClick={handlePopulate}
          className="mt-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-semibold rounded-md shadow transition-all"
        >
          Populate Data
        </button>
      )}
    </div>
  );
};

export default AbhaQRScanner;
