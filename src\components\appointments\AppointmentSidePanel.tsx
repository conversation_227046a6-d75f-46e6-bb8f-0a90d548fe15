import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { AppointmentForm } from './AppointmentForm';
import { getDoctors, type Doctor } from '../../services/scheduleApis';
import { getAppointmentsByProvider } from '../../services/appointmentApis';
import { showSuccess, showError } from '../../utils/toastUtils';
import type { Provider } from '../../types/provider';
import type { Appointment } from '../../types/appointment';

interface AppointmentSidePanelProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate?: string;
  selectedTime?: string;
  onAppointmentCreated: (appointment: Appointment) => void;
  editingAppointment?: Appointment | null;
  onProviderChange?: (providerId: string) => void;
}

const AppointmentSidePanel: React.FC<AppointmentSidePanelProps> = ({
  isOpen,
  onClose,
  selectedDate,
  selectedTime,
  onAppointmentCreated,
  editingAppointment,
  onProviderChange
}) => {
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [providerAppointments, setProviderAppointments] = useState<Appointment[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [bookedSlots, setBookedSlots] = useState<string[]>([]);

  useEffect(() => {
    if (isOpen) {
      loadProviders();
    }
  }, [isOpen]);

  useEffect(() => {
    if (selectedProvider) {
      loadProviderAppointments();
    }
  }, [selectedProvider, selectedDate]);

  // Transform Doctor to Provider format
  const transformDoctorToProvider = (doctor: Doctor): Provider => {
    const nameParts = doctor.fullName.split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    return {
      providerId: doctor.doctorId,
      facilityId: '',
      title: 'Dr.',
      firstName,
      lastName,
      email: '',
      phoneNumber: '',
      specialization: doctor.specialization as any,
      licenseNumber: doctor.registrationNumber,
      qualification: '',
      experience: doctor.yearsOfExperience,
      department: '',
      isActive: doctor.isActive,
      status: doctor.isActive ? 'Available' as any : 'Unavailable' as any,
      defaultSlotDuration: 30 as any,
      workingHours: [],
    };
  };

  const loadProviders = async () => {
    try {
      const response = await getDoctors();
      if (response.success && response.data) {
        const transformedProviders = response.data
          .filter(doctor => doctor.isActive)
          .map(transformDoctorToProvider);
        setProviders(transformedProviders);
      } else {
        console.error('Failed to load doctors:', response.error);
        showError('Failed to load providers');
      }
    } catch (error) {
      console.error('Failed to load providers:', error);
      showError('Failed to load providers');
    }
  };

  const loadProviderAppointments = async () => {
    if (!selectedProvider) {
      console.log('No provider selected, skipping appointment load');
      return;
    }

    try {
      console.log('🔍 Loading appointments for provider:', {
        providerId: selectedProvider.providerId,
        providerName: `${selectedProvider.firstName} ${selectedProvider.lastName}`,
        selectedDate: selectedDate,
        currentDate: new Date().toISOString().split('T')[0]
      });

      // Get appointments for the selected date or current date if no date selected
      const targetDate = selectedDate || new Date().toISOString().split('T')[0];
      const dateFrom = targetDate;
      const dateTo = targetDate;

      console.log('📅 Fetching appointments with date range:', { dateFrom, dateTo });

      let appointments = await getAppointmentsByProvider(
        selectedProvider.providerId,
        dateFrom,
        dateTo
      );

      console.log('📋 Received appointments from API (with date filter):', {
        count: appointments.length,
        appointments: appointments.map(apt => ({
          id: apt.appointmentId,
          date: apt.appointmentDate,
          time: apt.startTime,
          patient: apt.patient?.firstName + ' ' + apt.patient?.lastName,
          status: apt.status
        }))
      });

      // If no appointments found with date filter, try without date filter for debugging
      if (appointments.length === 0) {
        console.log('🔍 No appointments found with date filter, trying without date filter...');
        appointments = await getAppointmentsByProvider(selectedProvider.providerId);

        console.log('📋 Received appointments from API (without date filter):', {
          count: appointments.length,
          appointments: appointments.map(apt => ({
            id: apt.appointmentId,
            date: apt.appointmentDate,
            time: apt.startTime,
            patient: apt.patient?.firstName + ' ' + apt.patient?.lastName,
            status: apt.status
          }))
        });
      }

      setProviderAppointments(appointments);

      // Extract booked slots for the target date
      const dateAppointments = appointments.filter(apt => {
        // Handle different date formats from API
        let aptDate = apt.appointmentDate;
        if (aptDate.includes('T')) {
          aptDate = aptDate.split('T')[0]; // Extract date part
        }

        const matches = aptDate === targetDate && apt.status !== 'Cancelled';
        console.log(`🔍 Checking appointment: ${apt.appointmentId}`, {
          aptDate,
          targetDate,
          status: apt.status,
          matches
        });
        return matches;
      });

      console.log('✅ Filtered appointments for target date:', {
        targetDate,
        count: dateAppointments.length,
        appointments: dateAppointments
      });

      // Extract time slots (handle different time formats)
      const slots = dateAppointments.map(apt => {
        // Handle time format - extract HH:MM from various formats
        let timeSlot = apt.startTime;
        if (apt.startTime.includes('T')) {
          timeSlot = apt.startTime.split('T')[1].substring(0, 5);
        } else if (apt.startTime.includes(':')) {
          timeSlot = apt.startTime.substring(0, 5);
        }
        console.log(`⏰ Extracted time slot: ${apt.startTime} -> ${timeSlot}`);
        return timeSlot;
      });

      setBookedSlots(slots);
      console.log(`🎯 Final result - Found ${slots.length} booked slots for ${targetDate}:`, slots);

    } catch (error) {
      console.error('❌ Failed to load provider appointments:', error);
      setProviderAppointments([]);
      setBookedSlots([]);
    }
  };

  const handleProviderChange = (providerId: string) => {
    // Notify parent component about provider change
    onProviderChange?.(providerId);
    const provider = providers.find(p => p.providerId === providerId);
    setSelectedProvider(provider || null);
  };

  const handleAppointmentSuccess = (appointment: Appointment) => {
    onAppointmentCreated(appointment);
    showSuccess(editingAppointment ? 'Appointment updated successfully' : 'Appointment created successfully');
    onClose();
  };

  const isSlotBooked = (time: string) => {
    return bookedSlots.includes(time);
  };



  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop with blur effect */}
      <div className="fixed inset-0 z-50">
        <div
          className="absolute inset-0 bg-black bg-opacity-50 blur-backdrop"
          onClick={onClose}
        />
      </div>

      {/* Side panel container */}
      <div className="fixed inset-0 z-50 overflow-hidden pointer-events-none">
        <div className="absolute right-0 top-0 h-full w-full max-w-2xl bg-white shadow-2xl border-l border-gray-200 pointer-events-auto">
        {/* Single Column Layout */}
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {editingAppointment ? 'Edit Appointment' : 'Create New Appointment'}
              </h2>
              {selectedDate && (
                <p className="text-sm text-gray-600 mt-1">
                  {new Date(selectedDate).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                  {selectedTime && ` at ${selectedTime}`}
                </p>
              )}
            </div>
            <Button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600"
            >
              <X size={24} />
            </Button>
          </div>

          {/* Form Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <AppointmentForm
              selectedDate={selectedDate}
              selectedTime={selectedTime}
              onSuccess={handleAppointmentSuccess}
              onProviderChange={handleProviderChange}
              bookedSlots={bookedSlots}
              editingAppointment={editingAppointment}
              isSlotBooked={isSlotBooked}
            />
          </div>
        </div>
      </div>
      </div>
    </>
  );
};

export default AppointmentSidePanel;
